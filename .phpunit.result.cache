{"version": 1, "defects": {"Tests\\Feature\\SelfLearningSystemTest::it_can_store_new_qa_pair_in_knowledge_base": 7, "Tests\\Feature\\SelfLearningSystemTest::it_can_find_similar_questions_in_knowledge_base": 7, "Tests\\Feature\\SelfLearningSystemTest::it_can_query_knowledge_base_and_return_cached_answer": 8, "Tests\\Feature\\SelfLearningSystemTest::it_can_update_quality_rating_based_on_feedback": 7, "Tests\\Feature\\SelfLearningSystemTest::it_can_prune_low_quality_entries": 7, "Tests\\Feature\\SelfLearningSystemTest::it_can_get_learning_statistics": 7, "Tests\\Feature\\SelfLearningSystemTest::it_handles_exact_question_matches": 7, "Tests\\Feature\\SelfLearningSystemTest::it_respects_language_isolation": 7, "Tests\\Unit\\MockEmbeddingServiceTest::it_can_find_most_similar_embeddings": 8, "Tests\\Unit\\MockEmbeddingServiceTest::it_can_normalize_text_properly": 8, "Tests\\Unit\\MockEmbeddingServiceTest::it_handles_empty_text_gracefully": 7, "Tests\\Unit\\MockEmbeddingServiceTest::identical_texts_have_perfect_similarity": 7, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_filter_entries_by_language": 8, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_find_entries_by_exact_question_hash": 8, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_prune_low_quality_entries": 8, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_get_knowledge_base_statistics": 8, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_handle_chat_api_endpoints_structure": 7}, "times": {"Tests\\Feature\\SelfLearningSystemTest::it_can_store_new_qa_pair_in_knowledge_base": 0.03, "Tests\\Feature\\SelfLearningSystemTest::it_can_find_similar_questions_in_knowledge_base": 0.008, "Tests\\Feature\\SelfLearningSystemTest::it_can_query_knowledge_base_and_return_cached_answer": 0.007, "Tests\\Feature\\SelfLearningSystemTest::it_can_update_quality_rating_based_on_feedback": 0.004, "Tests\\Feature\\SelfLearningSystemTest::it_can_prune_low_quality_entries": 0.007, "Tests\\Feature\\SelfLearningSystemTest::it_can_get_learning_statistics": 0.007, "Tests\\Feature\\SelfLearningSystemTest::it_handles_exact_question_matches": 0.004, "Tests\\Feature\\SelfLearningSystemTest::it_respects_language_isolation": 0.006, "Tests\\Unit\\MockEmbeddingServiceTest::it_can_generate_mock_embeddings": 0.038, "Tests\\Unit\\MockEmbeddingServiceTest::it_can_calculate_similarity_between_embeddings": 0.004, "Tests\\Unit\\MockEmbeddingServiceTest::it_can_find_most_similar_embeddings": 0.022, "Tests\\Unit\\MockEmbeddingServiceTest::it_can_normalize_text_properly": 0.001, "Tests\\Unit\\MockEmbeddingServiceTest::it_can_get_embedding_dimensions": 0, "Tests\\Unit\\MockEmbeddingServiceTest::it_can_batch_generate_embeddings": 0.006, "Tests\\Unit\\MockEmbeddingServiceTest::it_handles_empty_text_gracefully": 0.007, "Tests\\Unit\\MockEmbeddingServiceTest::it_handles_very_long_text": 0.003, "Tests\\Unit\\MockEmbeddingServiceTest::identical_texts_have_perfect_similarity": 0.002, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_store_knowledge_base_entry_manually": 0.008, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_update_usage_count_when_entry_is_accessed": 0.011, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_update_quality_rating_based_on_feedback": 0.003, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_filter_entries_by_language": 0.004, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_find_entries_by_exact_question_hash": 0.004, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_prune_low_quality_entries": 0.007, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_get_knowledge_base_statistics": 0.007, "Tests\\Feature\\SelfLearningIntegrationTest::it_can_handle_chat_api_endpoints_structure": 0.02, "Tests\\Unit\\ExampleTest::test_that_true_is_true": 0.014, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.085}}