<?php

namespace App\Services;

use App\Models\KnowledgeBase;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class LearningService
{
    private EmbeddingService $embeddingService;
    private SimilaritySearchService $similaritySearchService;
    private float $confidenceThreshold;
    private bool $autoLearnEnabled;

    public function __construct(
        EmbeddingService $embeddingService,
        SimilaritySearchService $similaritySearchService
    ) {
        $this->embeddingService = $embeddingService;
        $this->similaritySearchService = $similaritySearchService;
        $this->confidenceThreshold = config('widdx.learning.confidence_threshold', 0.7);
        $this->autoLearnEnabled = config('widdx.learning.auto_learn_enabled', true);
    }

    /**
     * Learn from a new Q&A interaction
     */
    public function learnFromInteraction(
        string $question,
        string $answer,
        string $language = 'en',
        string $sourceModel = null,
        float $confidenceScore = 0.8,
        array $metadata = []
    ): ?KnowledgeBase {
        if (!$this->autoLearnEnabled) {
            return null;
        }

        try {
            DB::beginTransaction();

            // Check if we already have this exact question
            $questionHash = KnowledgeBase::generateQuestionHash($question, $language);
            $existingEntry = KnowledgeBase::byQuestionHash($questionHash)
                ->byLanguage($language)
                ->first();

            if ($existingEntry) {
                // Update existing entry
                $updatedEntry = $this->updateExistingEntry($existingEntry, $answer, $confidenceScore, $metadata);
                DB::commit();
                return $updatedEntry;
            }

            // Create new knowledge base entry
            $newEntry = $this->createNewEntry(
                $question,
                $answer,
                $language,
                $sourceModel,
                $confidenceScore,
                $metadata
            );

            DB::commit();

            Log::info('New knowledge learned', [
                'question' => substr($question, 0, 100),
                'language' => $language,
                'source_model' => $sourceModel,
                'confidence' => $confidenceScore,
                'knowledge_id' => $newEntry->id
            ]);

            return $newEntry;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to learn from interaction', [
                'question' => substr($question, 0, 100),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Query knowledge base before external API call
     */
    public function queryKnowledgeBase(
        string $question,
        string $language = 'en',
        float $minConfidence = null
    ): ?array {
        $minConfidence = $minConfidence ?? $this->confidenceThreshold;

        try {
            $knowledgeEntry = $this->similaritySearchService->searchSimilarQuestions(
                $question,
                $language,
                $minConfidence
            );

            if ($knowledgeEntry) {
                // Update usage statistics
                $knowledgeEntry->incrementUsage();

                Log::info('Knowledge base hit', [
                    'question' => substr($question, 0, 100),
                    'knowledge_id' => $knowledgeEntry->id,
                    'confidence' => $knowledgeEntry->confidence_score,
                    'usage_count' => $knowledgeEntry->usage_count + 1
                ]);

                return [
                    'answer' => $knowledgeEntry->answer,
                    'confidence' => $knowledgeEntry->confidence_score,
                    'source' => 'knowledge_base',
                    'knowledge_id' => $knowledgeEntry->id,
                    'usage_count' => $knowledgeEntry->usage_count,
                    'quality_rating' => $knowledgeEntry->quality_rating,
                    'metadata' => $knowledgeEntry->metadata
                ];
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Knowledge base query failed', [
                'question' => substr($question, 0, 100),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Update quality rating based on user feedback
     */
    public function updateQualityRating(int $knowledgeId, float $rating): bool
    {
        try {
            $entry = KnowledgeBase::find($knowledgeId);
            if (!$entry) {
                return false;
            }

            $entry->updateQualityRating($rating);

            Log::info('Quality rating updated', [
                'knowledge_id' => $knowledgeId,
                'new_rating' => $rating
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update quality rating', [
                'knowledge_id' => $knowledgeId,
                'rating' => $rating,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Prune low-quality or outdated knowledge
     */
    public function pruneKnowledgeBase(array $criteria = []): array
    {
        $defaultCriteria = [
            'min_quality_rating' => 2.0,
            'max_age_days' => 365,
            'min_usage_count' => 1,
            'max_confidence_score' => 0.3
        ];

        $criteria = array_merge($defaultCriteria, $criteria);

        try {
            $query = KnowledgeBase::query();

            $query->where(function ($mainQuery) use ($criteria) {
                // Remove entries with very low quality ratings
                if (isset($criteria['min_quality_rating'])) {
                    $mainQuery->orWhere(function ($q) use ($criteria) {
                        $q->where(function ($subQ) use ($criteria) {
                            $subQ->whereNull('quality_rating')
                                 ->orWhere('quality_rating', '<', $criteria['min_quality_rating']);
                        });
                        
                        // Also check age if specified
                        if (isset($criteria['max_age_days'])) {
                            $q->where('created_at', '<', Carbon::now()->subDays($criteria['max_age_days']));
                        }
                    });
                }

                // Remove entries with very low confidence and low usage
                if (isset($criteria['max_confidence_score']) && isset($criteria['min_usage_count'])) {
                    $mainQuery->orWhere(function ($q) use ($criteria) {
                        $q->where('confidence_score', '<', $criteria['max_confidence_score'])
                          ->where('usage_count', '<', $criteria['min_usage_count']);
                    });
                }
            });

            $deletedCount = $query->count();
            $query->delete();

            Log::info('Knowledge base pruned', [
                'deleted_count' => $deletedCount,
                'criteria' => $criteria
            ]);

            return ['pruned' => $deletedCount];
        } catch (\Exception $e) {
            Log::error('Knowledge base pruning failed', [
                'criteria' => $criteria,
                'error' => $e->getMessage()
            ]);
            return ['pruned' => 0];
        }
    }

    /**
     * Get learning statistics
     */
    public function getLearningStats(int $days = 30): array
    {
        try {
            $since = Carbon::now()->subDays($days);

            $stats = [
                'total_entries' => KnowledgeBase::count(),
                'new_entries_period' => KnowledgeBase::where('created_at', '>=', $since)->count(),
                'api_calls_saved' => KnowledgeBase::sum('usage_count'),
                'avg_confidence_score' => round(KnowledgeBase::avg('confidence_score'), 3),
                'average_quality' => round(KnowledgeBase::whereNotNull('quality_rating')->avg('quality_rating'), 2),
                'by_language' => KnowledgeBase::groupBy('language')
                    ->selectRaw('language, count(*) as count')
                    ->pluck('count', 'language')
                    ->toArray(),
                'by_model' => KnowledgeBase::whereNotNull('source_model')
                    ->groupBy('source_model')
                    ->selectRaw('source_model, count(*) as count')
                    ->pluck('count', 'source_model')
                    ->toArray(),
                'top_used_entries' => KnowledgeBase::orderBy('usage_count', 'desc')
                    ->limit(10)
                    ->get(['id', 'question', 'usage_count', 'quality_rating'])
                    ->map(function ($entry) {
                        return [
                            'id' => $entry->id,
                            'question' => substr($entry->question, 0, 100),
                            'usage_count' => $entry->usage_count,
                            'quality_rating' => $entry->quality_rating
                        ];
                    })
                    ->toArray()
            ];

            return $stats;
        } catch (\Exception $e) {
            Log::error('Failed to get learning stats', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Create new knowledge base entry
     */
    private function createNewEntry(
        string $question,
        string $answer,
        string $language,
        ?string $sourceModel,
        float $confidenceScore,
        array $metadata
    ): KnowledgeBase {
        // Generate embedding for the question
        $embedding = $this->embeddingService->generateEmbedding($question, $language);
        
        $entry = KnowledgeBase::create([
            'question' => $question,
            'answer' => $answer,
            'question_embedding' => $embedding,
            'language' => $language,
            'confidence_score' => $confidenceScore,
            'source_model' => $sourceModel,
            'metadata' => $metadata,
            'question_hash' => KnowledgeBase::generateQuestionHash($question, $language),
            'last_used_at' => Carbon::now()
        ]);

        return $entry;
    }

    /**
     * Update existing knowledge base entry
     */
    private function updateExistingEntry(
        KnowledgeBase $entry,
        string $newAnswer,
        float $newConfidenceScore,
        array $metadata
    ): KnowledgeBase {
        // If new answer has higher confidence, update it
        if ($newConfidenceScore > $entry->confidence_score) {
            $entry->update([
                'answer' => $newAnswer,
                'confidence_score' => $newConfidenceScore,
                'metadata' => array_merge($entry->metadata ?? [], $metadata),
                'last_used_at' => Carbon::now()
            ]);
        } else {
            // Just update usage and metadata
            $entry->update([
                'metadata' => array_merge($entry->metadata ?? [], $metadata),
                'last_used_at' => Carbon::now()
            ]);
        }

        $entry->incrementUsage();
        return $entry;
    }

    /**
     * Enable or disable auto-learning
     */
    public function setAutoLearning(bool $enabled): void
    {
        $this->autoLearnEnabled = $enabled;
    }

    /**
     * Set confidence threshold for knowledge base queries
     */
    public function setConfidenceThreshold(float $threshold): void
    {
        $this->confidenceThreshold = max(0.0, min(1.0, $threshold));
    }
}