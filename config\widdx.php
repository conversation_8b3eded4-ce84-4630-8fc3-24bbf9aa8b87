<?php

return [
    /*
    |--------------------------------------------------------------------------
    | WIDDX AI Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration settings for the WIDDX AI platform including API keys,
    | service endpoints, performance settings, and feature flags.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | DeepSeek API Configuration
    |--------------------------------------------------------------------------
    */
    'deepseek' => [
        'api_key' => env('DEEPSEEK_API_KEY'),
        'base_url' => env('DEEPSEEK_BASE_URL', 'https://api.deepseek.com'),
        'timeout' => env('DEEPSEEK_TIMEOUT', 30),
        'max_retries' => env('DEEPSEEK_MAX_RETRIES', 3),
        'retry_delay' => env('DEEPSEEK_RETRY_DELAY', 1000), // milliseconds
        'cache_ttl' => env('DEEPSEEK_CACHE_TTL', 3600), // seconds
        'max_results' => env('DEEPSEEK_MAX_RESULTS', 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Gemini API Configuration
    |--------------------------------------------------------------------------
    */
    'gemini' => [
        'api_key' => env('GEMINI_API_KEY'),
        'base_url' => env('GEMINI_BASE_URL', 'https://generativelanguage.googleapis.com'),
        'model' => env('GEMINI_MODEL', 'gemini-pro'),
        'timeout' => env('GEMINI_TIMEOUT', 30),
        'max_retries' => env('GEMINI_MAX_RETRIES', 3),
        'retry_delay' => env('GEMINI_RETRY_DELAY', 1000), // milliseconds
        'cache_ttl' => env('GEMINI_CACHE_TTL', 1800), // seconds
        'max_tokens' => env('GEMINI_MAX_TOKENS', 2048),
        'temperature' => env('GEMINI_TEMPERATURE', 0.7),
        'top_p' => env('GEMINI_TOP_P', 0.8),
        'top_k' => env('GEMINI_TOP_K', 40),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'target_response_time' => env('WIDDX_TARGET_RESPONSE_TIME', 2000), // milliseconds
        'max_processing_time' => env('WIDDX_MAX_PROCESSING_TIME', 30000), // milliseconds
        'enable_caching' => env('WIDDX_ENABLE_CACHING', true),
        'cache_driver' => env('WIDDX_CACHE_DRIVER', 'redis'),
        'enable_compression' => env('WIDDX_ENABLE_COMPRESSION', true),
        'parallel_processing' => env('WIDDX_PARALLEL_PROCESSING', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Quality & Accuracy Settings
    |--------------------------------------------------------------------------
    */
    'quality' => [
        'target_accuracy' => env('WIDDX_TARGET_ACCURACY', 90), // percentage
        'min_quality_score' => env('WIDDX_MIN_QUALITY_SCORE', 0.7),
        'enable_quality_monitoring' => env('WIDDX_ENABLE_QUALITY_MONITORING', true),
        'fallback_enabled' => env('WIDDX_FALLBACK_ENABLED', true),
        'source_verification' => env('WIDDX_SOURCE_VERIFICATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Language Support
    |--------------------------------------------------------------------------
    */
    'languages' => [
        'supported' => ['en', 'ar'],
        'default' => env('WIDDX_DEFAULT_LANGUAGE', 'en'),
        'auto_detect' => env('WIDDX_AUTO_DETECT_LANGUAGE', true),
        'translation_enabled' => env('WIDDX_TRANSLATION_ENABLED', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'rate_limiting' => [
            'enabled' => env('WIDDX_RATE_LIMITING_ENABLED', true),
            'max_requests_per_minute' => env('WIDDX_MAX_REQUESTS_PER_MINUTE', 60),
            'max_requests_per_hour' => env('WIDDX_MAX_REQUESTS_PER_HOUR', 1000),
        ],
        'input_validation' => [
            'max_message_length' => env('WIDDX_MAX_MESSAGE_LENGTH', 4000),
            'allowed_content_types' => ['application/json', 'text/plain'],
            'sanitize_input' => env('WIDDX_SANITIZE_INPUT', true),
        ],
        'api_key_rotation' => [
            'enabled' => env('WIDDX_API_KEY_ROTATION_ENABLED', false),
            'rotation_interval' => env('WIDDX_API_KEY_ROTATION_INTERVAL', 86400), // seconds
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring & Logging
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'enabled' => env('WIDDX_MONITORING_ENABLED', true),
        'log_level' => env('WIDDX_LOG_LEVEL', 'info'),
        'metrics_collection' => env('WIDDX_METRICS_COLLECTION', true),
        'performance_tracking' => env('WIDDX_PERFORMANCE_TRACKING', true),
        'error_reporting' => env('WIDDX_ERROR_REPORTING', true),
        'health_check_interval' => env('WIDDX_HEALTH_CHECK_INTERVAL', 300), // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Chat Settings
    |--------------------------------------------------------------------------
    */
    'chat' => [
        'max_conversation_length' => env('WIDDX_MAX_CONVERSATION_LENGTH', 100),
        'conversation_timeout' => env('WIDDX_CONVERSATION_TIMEOUT', 3600), // seconds
        'auto_title_generation' => env('WIDDX_AUTO_TITLE_GENERATION', true),
        'message_history_limit' => env('WIDDX_MESSAGE_HISTORY_LIMIT', 50),
        'enable_context_awareness' => env('WIDDX_ENABLE_CONTEXT_AWARENESS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Embedding Configuration
    |--------------------------------------------------------------------------
    */
    'embedding' => [
        'provider' => env('WIDDX_EMBEDDING_PROVIDER', 'local'), // openai, local, sentence_transformer
        'providers' => [
            'openai' => [
                'api_key' => env('OPENAI_API_KEY'),
                'model' => env('OPENAI_EMBEDDING_MODEL', 'text-embedding-3-small'),
                'timeout' => env('OPENAI_EMBEDDING_TIMEOUT', 30),
            ],
            'local' => [
                'dimension' => env('LOCAL_EMBEDDING_DIMENSION', 384),
            ],
            'sentence_transformer' => [
                'endpoint' => env('SENTENCE_TRANSFORMER_ENDPOINT', 'http://localhost:8080/embed'),
                'model' => env('SENTENCE_TRANSFORMER_MODEL', 'all-MiniLM-L6-v2'),
                'dimension' => env('SENTENCE_TRANSFORMER_DIMENSION', 384),
                'timeout' => env('SENTENCE_TRANSFORMER_TIMEOUT', 30),
            ],
        ],
        'cache_ttl' => env('WIDDX_EMBEDDING_CACHE_TTL', 86400), // 24 hours
    ],

    /*
    |--------------------------------------------------------------------------
    | Similarity Search Configuration
    |--------------------------------------------------------------------------
    */
    'similarity' => [
        'threshold' => env('WIDDX_SIMILARITY_THRESHOLD', 0.75),
        'max_results' => env('WIDDX_SIMILARITY_MAX_RESULTS', 5),
        'enable_exact_match' => env('WIDDX_ENABLE_EXACT_MATCH', true),
        'candidate_limit' => env('WIDDX_SIMILARITY_CANDIDATE_LIMIT', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Learning Configuration
    |--------------------------------------------------------------------------
    */
    'learning' => [
        'auto_learn_enabled' => env('WIDDX_AUTO_LEARN_ENABLED', true),
        'confidence_threshold' => env('WIDDX_LEARNING_CONFIDENCE_THRESHOLD', 0.7),
        'min_quality_for_learning' => env('WIDDX_MIN_QUALITY_FOR_LEARNING', 0.6),
        'max_knowledge_entries' => env('WIDDX_MAX_KNOWLEDGE_ENTRIES', 100000),
        'pruning' => [
            'enabled' => env('WIDDX_KNOWLEDGE_PRUNING_ENABLED', true),
            'schedule' => env('WIDDX_KNOWLEDGE_PRUNING_SCHEDULE', 'weekly'),
            'min_quality_rating' => env('WIDDX_PRUNING_MIN_QUALITY', 2.0),
            'max_age_days' => env('WIDDX_PRUNING_MAX_AGE_DAYS', 365),
            'min_usage_count' => env('WIDDX_PRUNING_MIN_USAGE', 1),
            'max_confidence_score' => env('WIDDX_PRUNING_MAX_CONFIDENCE', 0.3),
        ],
        'feedback' => [
            'enabled' => env('WIDDX_FEEDBACK_ENABLED', true),
            'require_rating' => env('WIDDX_REQUIRE_RATING', false),
            'auto_improve' => env('WIDDX_AUTO_IMPROVE_ENABLED', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Knowledge Base Configuration
    |--------------------------------------------------------------------------
    */
    'knowledge_base' => [
        'enabled' => env('WIDDX_KNOWLEDGE_BASE_ENABLED', true),
        'priority_over_api' => env('WIDDX_KB_PRIORITY_OVER_API', true),
        'fallback_to_api' => env('WIDDX_KB_FALLBACK_TO_API', true),
        'min_confidence_for_response' => env('WIDDX_KB_MIN_CONFIDENCE', 0.75),
        'max_response_age_days' => env('WIDDX_KB_MAX_RESPONSE_AGE', 90),
        'enable_context_matching' => env('WIDDX_KB_CONTEXT_MATCHING', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    */
    'features' => [
        'experimental_mode' => env('WIDDX_EXPERIMENTAL_MODE', false),
        'debug_mode' => env('WIDDX_DEBUG_MODE', false),
        'maintenance_mode' => env('WIDDX_MAINTENANCE_MODE', false),
        'beta_features' => env('WIDDX_BETA_FEATURES', false),
        'self_learning' => env('WIDDX_SELF_LEARNING_ENABLED', true),
        'similarity_search' => env('WIDDX_SIMILARITY_SEARCH_ENABLED', true),
        'knowledge_base' => env('WIDDX_KNOWLEDGE_BASE_FEATURE', true),
    ],
];
