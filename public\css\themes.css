/* WIDDX AI - Elite Theme System */
/* Advanced Dark/Light Theme Definitions */

/* Elite Dark Theme */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-quaternary: #475569;
    --bg-chat: #0f172a;
    --bg-input: #1e293b;
    --bg-sidebar: #020617;
    --bg-glass: rgba(15, 23, 42, 0.8);
    --bg-overlay: rgba(255, 255, 255, 0.05);
    
    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-tertiary: #cbd5e1;
    --text-quaternary: #94a3b8;
    --text-muted: #64748b;
    --text-inverse: #0f172a;
    --text-accent: #60a5fa;
    
    --brand-gradient: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
    --brand-gradient-subtle: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #1d4ed8 100%);
    
    --ai-bg: #1e293b;
    --ai-bg-hover: #334155;
    --ai-border: #475569;
    --ai-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    
    --hover-bg: #1e293b;
    --hover-border: #475569;
    --active-bg: #334155;
    --active-border: #64748b;
    --focus-ring: 0 0 0 3px rgba(96, 165, 250, 0.2);
    
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px -1px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -2px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -4px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 8px 10px -6px rgba(0, 0, 0, 0.4);
}

/* Theme Transition Effects */
* {
    transition: 
        background-color var(--transition-normal),
        border-color var(--transition-normal),
        color var(--transition-normal),
        box-shadow var(--transition-normal);
}

/* Theme-specific Component Overrides */
[data-theme="dark"] .conversation-item.active {
    background: rgba(59, 130, 246, 0.1);
    border-color: var(--brand-primary);
    color: var(--text-accent);
}

[data-theme="dark"] .error-alert {
    background: linear-gradient(135deg, #450a0a 0%, #7f1d1d 100%);
    border-color: #7f1d1d;
    color: #fca5a5;
}

/* Theme Toggle Animation */
.theme-toggle i {
    transition: transform var(--transition-normal);
}

.theme-toggle:hover i {
    transform: rotate(180deg);
}

/* Dark Mode Specific Animations */
[data-theme="dark"] .empty-state::before {
    background: var(--brand-gradient);
    opacity: 0.08;
}

[data-theme="dark"] .sidebar::before {
    background: var(--bg-glass);
}

[data-theme="dark"] .chat-header::before {
    background: var(--bg-glass);
}

[data-theme="dark"] .chat-input-container::before {
    background: var(--bg-glass);
}

/* Theme-aware Scrollbars */
[data-theme="dark"] .conversations-list::-webkit-scrollbar-thumb {
    background: var(--bg-quaternary);
}

[data-theme="dark"] .conversations-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-quaternary);
}

[data-theme="dark"] .chat-messages::-webkit-scrollbar-thumb {
    background: var(--bg-quaternary);
}

[data-theme="dark"] .chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--text-quaternary);
}

/* Theme-specific Focus States */
[data-theme="dark"] .focus-visible {
    outline: 2px solid var(--text-accent);
    outline-offset: 2px;
}

/* Theme Persistence */
body {
    transition: background-color var(--transition-slow);
}

/* Print Mode Theme Override */
@media print {
    [data-theme="dark"] {
        --bg-primary: #ffffff;
        --bg-secondary: #ffffff;
        --text-primary: #000000;
        --text-secondary: #333333;
    }
}
