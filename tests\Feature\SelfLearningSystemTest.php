<?php

namespace Tests\Feature;

use App\Models\KnowledgeBase;
use App\Models\User;
use App\Services\LearningService;
use App\Services\SimilaritySearchService;
use App\Services\EmbeddingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SelfLearningSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private User $user;
    private LearningService $learningService;
    private SimilaritySearchService $similaritySearchService;
    private EmbeddingService $embeddingService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        // Mock the EmbeddingService for testing
        $this->embeddingService = $this->createMock(EmbeddingService::class);
        $this->embeddingService->method('generateEmbedding')
            ->willReturnCallback(function($text, $language) {
                // Return a simple dummy embedding based on text length and content
                $hash = hash('sha256', $text);
                $embedding = [];
                for ($i = 0; $i < 384; $i++) {
                    $embedding[] = (float) (hexdec(substr($hash, $i % 64, 1)) / 15.0 - 0.5);
                }
                return $embedding;
            });
        
        $this->embeddingService->method('calculateSimilarity')
            ->willReturnCallback(function($vector1, $vector2) {
                // Return a similarity score above threshold for testing
                $dotProduct = 0;
                $magnitude1 = 0;
                $magnitude2 = 0;
                for ($i = 0; $i < min(count($vector1), count($vector2)); $i++) {
                    $dotProduct += $vector1[$i] * $vector2[$i];
                    $magnitude1 += $vector1[$i] * $vector1[$i];
                    $magnitude2 += $vector2[$i] * $vector2[$i];
                }
                $magnitude1 = sqrt($magnitude1);
                $magnitude2 = sqrt($magnitude2);
                $similarity = ($magnitude1 > 0 && $magnitude2 > 0) ? $dotProduct / ($magnitude1 * $magnitude2) : 0;
                return max(0.8, $similarity); // Ensure similarity is above threshold
            });
        
        $this->app->instance(EmbeddingService::class, $this->embeddingService);
        
        $this->learningService = app(LearningService::class);
        $this->similaritySearchService = app(SimilaritySearchService::class);
        
        // Set a lower similarity threshold for testing
        $this->similaritySearchService->setSimilarityThreshold(0.3);
        
        // Enable self-learning for tests
        config([
            'widdx.features.self_learning' => true,
            'widdx.features.similarity_search' => true,
            'widdx.features.knowledge_base' => true,
            'widdx.learning.auto_learn' => true,
            'widdx.similarity.threshold' => 0.3,
        ]);
    }

    /** @test */
    public function it_can_store_new_qa_pair_in_knowledge_base()
    {
        $question = 'What is Laravel?';
        $answer = 'Laravel is a PHP web application framework.';
        $language = 'en';
        $confidence = 0.9;
        $sourceModel = 'gemini-pro';

        $result = $this->learningService->learnFromInteraction(
            $question,
            $answer,
            $language,
            $sourceModel,
            $confidence
        );

        $this->assertInstanceOf(\App\Models\KnowledgeBase::class, $result);
        $this->assertDatabaseHas('knowledge_bases', [
            'question' => $question,
            'answer' => $answer,
            'language' => $language,
            'confidence_score' => $confidence,
            'source_model' => $sourceModel,
        ]);
    }

    /** @test */
    public function it_can_find_similar_questions_in_knowledge_base()
    {
        // Create test knowledge base entries with embeddings
        $question1 = 'What is Laravel?';
        $question2 = 'How to use Laravel?';
        
        $embedding1 = $this->embeddingService->generateEmbedding($question1, 'en');
        $embedding2 = $this->embeddingService->generateEmbedding($question2, 'en');
        
        KnowledgeBase::create([
            'question_hash' => KnowledgeBase::generateQuestionHash($question1, 'en'),
            'question' => $question1,
            'answer' => 'Laravel is a PHP web application framework.',
            'language' => 'en',
            'confidence_score' => 0.9,
            'quality_rating' => 0.8,
            'source_model' => 'gemini-pro',
            'usage_count' => 5,
            'question_embedding' => $embedding1,
        ]);

        KnowledgeBase::create([
            'question_hash' => KnowledgeBase::generateQuestionHash($question2, 'en'),
            'question' => $question2,
            'answer' => 'Laravel can be used to build web applications quickly.',
            'language' => 'en',
            'confidence_score' => 0.85,
            'quality_rating' => 0.75,
            'source_model' => 'gemini-pro',
            'usage_count' => 3,
            'question_embedding' => $embedding2,
        ]);

        $results = $this->similaritySearchService->searchMultipleSimilarQuestions(
            'What is Laravel framework?',
            'en',
            0.5
        );

        $this->assertNotEmpty($results);
        $this->assertArrayHasKey('entry', $results[0]);
        $this->assertArrayHasKey('similarity', $results[0]);
        $this->assertInstanceOf(\App\Models\KnowledgeBase::class, $results[0]['entry']);
    }

    /** @test */
    public function it_can_query_knowledge_base_and_return_cached_answer()
    {
        // Store a Q&A pair
        $question = 'What is PHP?';
        $answer = 'PHP is a server-side scripting language.';
        
        KnowledgeBase::create([
            'question_hash' => KnowledgeBase::generateQuestionHash($question, 'en'),
            'question' => $question,
            'answer' => $answer,
            'language' => 'en',
            'confidence_score' => 0.9,
            'quality_rating' => 0.8,
            'source_model' => 'gemini-pro',
            'usage_count' => 0,
        ]);

        $result = $this->learningService->queryKnowledgeBase($question, 'en');

        $this->assertNotNull($result);
        $this->assertEquals($answer, $result['answer']);
        $this->assertGreaterThan(0.8, $result['confidence']);
        
        // Verify usage count was incremented
        $entry = KnowledgeBase::where('question_hash', KnowledgeBase::generateQuestionHash($question, 'en'))->first();
        $this->assertEquals(1, $entry->usage_count);
    }

    /** @test */
    public function it_can_update_quality_rating_based_on_feedback()
    {
        $entry = KnowledgeBase::create([
            'question_hash' => KnowledgeBase::generateQuestionHash('Test question', 'en'),
            'question' => 'Test question',
            'answer' => 'Test answer',
            'language' => 'en',
            'confidence_score' => 0.8,
            'quality_rating' => 0.5,
            'source_model' => 'gemini-pro',
            'usage_count' => 1,
        ]);

        $this->learningService->updateQualityRating($entry->id, 0.9, 'Great answer!');

        $entry->refresh();
        $this->assertGreaterThan(0.5, $entry->quality_rating);
    }

    /** @test */
    public function it_can_prune_low_quality_entries()
    {
        // Create low-quality entries
        KnowledgeBase::create([
            'question_hash' => KnowledgeBase::generateQuestionHash('Low quality question 1', 'en'),
            'question' => 'Low quality question 1',
            'answer' => 'Poor answer',
            'language' => 'en',
            'confidence_score' => 0.3,
            'quality_rating' => 0.2,
            'source_model' => 'gemini-pro',
            'usage_count' => 0,
            'created_at' => now()->subDays(100),
        ]);

        KnowledgeBase::create([
            'question_hash' => KnowledgeBase::generateQuestionHash('High quality question', 'en'),
            'question' => 'High quality question',
            'answer' => 'Excellent answer',
            'language' => 'en',
            'confidence_score' => 0.9,
            'quality_rating' => 0.9,
            'source_model' => 'gemini-pro',
            'usage_count' => 10,
        ]);

        $result = $this->learningService->pruneKnowledgeBase([
            'min_quality_rating' => 0.3,
            'max_age_days' => 90,
            'min_usage_count' => 1,
            'max_confidence_score' => 0.5,
        ]);

        $this->assertGreaterThan(0, $result['pruned']);
        $this->assertDatabaseMissing('knowledge_bases', [
            'question' => 'Low quality question 1',
        ]);
        $this->assertDatabaseHas('knowledge_bases', [
            'question' => 'High quality question',
        ]);
    }

    /** @test */
    public function it_can_get_learning_statistics()
    {
        // Create test entries
        KnowledgeBase::create([
            'question_hash' => KnowledgeBase::generateQuestionHash('Question 1', 'en'),
            'question' => 'Question 1',
            'answer' => 'Answer 1',
            'language' => 'en',
            'confidence_score' => 0.8,
            'quality_rating' => 0.7,
            'source_model' => 'gemini-pro',
            'usage_count' => 5,
        ]);

        KnowledgeBase::create([
            'question_hash' => KnowledgeBase::generateQuestionHash('Question 2', 'en'),
            'question' => 'Question 2',
            'answer' => 'Answer 2',
            'language' => 'es',
            'confidence_score' => 0.9,
            'quality_rating' => 0.8,
            'source_model' => 'gemini-pro',
            'usage_count' => 3,
        ]);

        $stats = $this->learningService->getLearningStats();

        $this->assertArrayHasKey('total_entries', $stats);
        $this->assertArrayHasKey('api_calls_saved', $stats);
        $this->assertArrayHasKey('average_quality', $stats);
        $this->assertArrayHasKey('by_language', $stats);
        
        $this->assertEquals(2, $stats['total_entries']);
        $this->assertEquals(8, $stats['api_calls_saved']); // Total usage count
        $this->assertArrayHasKey('en', $stats['by_language']);
        $this->assertArrayHasKey('es', $stats['by_language']);
    }

    /** @test */
    public function it_handles_exact_question_matches()
    {
        $question = 'Exact match question';
        $answer = 'Exact match answer';
        
        KnowledgeBase::create([
            'question_hash' => KnowledgeBase::generateQuestionHash($question, 'en'),
            'question' => $question,
            'answer' => $answer,
            'language' => 'en',
            'confidence_score' => 0.9,
            'quality_rating' => 0.8,
            'source_model' => 'gemini-pro',
            'usage_count' => 0,
        ]);

        $result = $this->similaritySearchService->searchSimilarQuestions(
            $question, // Exact same question
            'en',
            0.5
        );

        $this->assertNotNull($result);
        $this->assertEquals($answer, $result->answer);
    }

    /** @test */
    public function it_respects_language_isolation()
    {
        // Create entries in different languages
        KnowledgeBase::create([
            'question_hash' => KnowledgeBase::generateQuestionHash('English question', 'en'),
            'question' => 'English question',
            'answer' => 'English answer',
            'language' => 'en',
            'confidence_score' => 0.9,
            'quality_rating' => 0.8,
            'source_model' => 'gemini-pro',
            'usage_count' => 1,
        ]);

        KnowledgeBase::create([
            'question_hash' => KnowledgeBase::generateQuestionHash('Pregunta en español', 'es'),
            'question' => 'Pregunta en español',
            'answer' => 'Respuesta en español',
            'language' => 'es',
            'confidence_score' => 0.9,
            'quality_rating' => 0.8,
            'source_model' => 'gemini-pro',
            'usage_count' => 1,
        ]);

        // Search in English should only return English results
        $englishResult = $this->similaritySearchService->searchSimilarQuestions(
            'English question',
            'en',
            0.5
        );

        // Search in Spanish should only return Spanish results
        $spanishResult = $this->similaritySearchService->searchSimilarQuestions(
            'Pregunta en español',
            'es',
            0.5
        );

        $this->assertNotNull($englishResult);
        $this->assertNotNull($spanishResult);
        $this->assertEquals('en', $englishResult->language);
        $this->assertEquals('es', $spanishResult->language);
    }
}