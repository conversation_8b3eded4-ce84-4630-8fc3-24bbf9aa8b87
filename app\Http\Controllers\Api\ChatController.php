<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Conversation;
use App\Models\Message;
use App\Services\WiddxAiService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Exception;

class ChatController extends Controller
{
    private WiddxAiService $widdxAiService;

    public function __construct(WiddxAiService $widdxAiService)
    {
        $this->widdxAiService = $widdxAiService;
    }

    /**
     * Send a message and get AI response
     */
    public function sendMessage(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'message' => 'required|string|max:2000',
                'session_id' => 'nullable|uuid',
                'conversation_id' => 'nullable|uuid',
                'language' => 'nullable|string|in:en,ar'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $message = $request->input('message');
            $sessionId = $request->input('session_id') ?: $request->input('conversation_id');
            $language = $request->input('language', 'en');

            // Fix encoding issues: Ensure proper UTF-8 handling
            if (!mb_check_encoding($message, 'UTF-8')) {
                // Try to detect and convert encoding
                $detected = mb_detect_encoding($message, ['UTF-8', 'ISO-8859-1', 'Windows-1252'], true);
                if ($detected && $detected !== 'UTF-8') {
                    $message = mb_convert_encoding($message, 'UTF-8', $detected);
                    Log::info('Converted message encoding', [
                        'from' => $detected,
                        'to' => 'UTF-8',
                        'message' => $message
                    ]);
                }
            }

            // Additional check: If still corrupted, try to get raw input
            if (strpos($message, '?') !== false && $language === 'ar') {
                $rawInput = file_get_contents('php://input');
                $jsonData = json_decode($rawInput, true);
                if (isset($jsonData['message']) && $jsonData['message'] !== $message) {
                    $message = $jsonData['message'];
                    Log::info('Used raw input for Arabic message', [
                        'original' => $request->input('message'),
                        'corrected' => $message
                    ]);
                }
            }

            // Find or create conversation
            $conversation = $this->findOrCreateConversation($sessionId, $language, $request);

            // Store user message
            $userMessage = Message::createUserMessage(
                $conversation->id,
                $message,
                $language
            );

            // Process query through WIDDX AI
            $aiResponse = $this->widdxAiService->processQuery(
                $message,
                $language,
                $conversation->session_id
            );

            // Store AI response
            $aiMessage = Message::createAiMessage(
                $conversation->id,
                $aiResponse
            );

            // Update conversation activity
            $conversation->updateActivity();
            $conversation->generateTitle();

            Log::info('Chat message processed successfully', [
                'session_id' => $conversation->session_id,
                'user_message_id' => $userMessage->id,
                'ai_message_id' => $aiMessage->id,
                'processing_time' => $aiResponse['processing_time']
            ]);

            return response()->json([
                'success' => true,
                'response' => $aiMessage->content,
                'conversation_id' => $conversation->session_id,
                'conversation_title' => $conversation->title,
                'data' => [
                    'session_id' => $conversation->session_id,
                    'conversation_id' => $conversation->session_id,
                    'user_message' => [
                        'id' => $userMessage->id,
                        'content' => $userMessage->content,
                        'timestamp' => $userMessage->created_at->toISOString()
                    ],
                    'ai_response' => [
                        'id' => $aiMessage->id,
                        'content' => $aiMessage->content,
                        'processing_time' => $aiMessage->processing_time,
                        'sources_used' => $aiMessage->sources_used,
                        'quality_score' => $aiMessage->quality_score,
                        'timestamp' => $aiMessage->created_at->toISOString()
                    ],
                    'metadata' => [
                        'language' => $language,
                        'model_info' => $aiResponse['model_info'],
                        'performance' => $aiResponse['performance']
                    ]
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Chat message processing failed', [
                'message' => $message ?? 'unknown',
                'session_id' => $sessionId ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your message. Please try again.',
                'error_code' => 'PROCESSING_ERROR'
            ], 500);
        }
    }

    /**
     * Get conversation history
     */
    public function getConversation(Request $request, string $identifier): JsonResponse
    {
        try {
            // Try to find by ID first, then by session_id
            $conversation = Conversation::where('id', $identifier)
                ->orWhere('session_id', $identifier)
                ->with(['messages' => function ($query) {
                    $query->orderBy('created_at', 'asc');
                }])
                ->first();

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Conversation not found'
                ], 404);
            }

            $messages = $conversation->messages->map(function ($message) {
                return [
                    'id' => $message->id,
                    'type' => $message->type,
                    'content' => $message->content,
                    'language' => $message->language,
                    'processing_time' => $message->processing_time,
                    'sources_used' => $message->sources_used,
                    'quality_score' => $message->quality_score,
                    'timestamp' => $message->created_at->toISOString(),
                    'has_errors' => $message->hasErrors()
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'conversation' => [
                        'id' => $conversation->id,
                        'session_id' => $conversation->session_id,
                        'title' => $conversation->title,
                        'language' => $conversation->language,
                        'total_messages' => $conversation->total_messages,
                        'last_activity' => $conversation->last_activity_at?->toISOString(),
                        'created_at' => $conversation->created_at->toISOString()
                    ],
                    'messages' => $messages,
                    'statistics' => $conversation->getStatistics()
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to retrieve conversation', [
                'identifier' => $identifier,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve conversation'
            ], 500);
        }
    }

    /**
     * Get recent conversations
     */
    public function getRecentConversations(Request $request): JsonResponse
    {
        try {
            $limit = $request->input('limit', 20);
            $language = $request->input('language');

            $query = Conversation::with('latestMessage')
                ->orderBy('last_activity_at', 'desc');

            if ($language) {
                $query->byLanguage($language);
            }

            $conversations = $query->limit($limit)->get();

            $data = $conversations->map(function ($conversation) {
                return [
                    'id' => $conversation->id,
                    'session_id' => $conversation->session_id,
                    'title' => $conversation->title,
                    'language' => $conversation->language,
                    'total_messages' => $conversation->total_messages,
                    'last_activity' => $conversation->last_activity_at?->toISOString(),
                    'created_at' => $conversation->created_at->toISOString(),
                    'latest_message' => $conversation->latestMessage ? [
                        'content' => Str::limit($conversation->latestMessage->content, 100),
                        'type' => $conversation->latestMessage->type,
                        'timestamp' => $conversation->latestMessage->created_at->toISOString()
                    ] : null
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $data
            ]);

        } catch (Exception $e) {
            Log::error('Failed to retrieve recent conversations', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve conversations'
            ], 500);
        }
    }

    /**
     * Rate response quality for learning
     */
    public function rateResponse(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'message_id' => 'required|exists:messages,id',
                'rating' => 'required|numeric|min:0|max:1',
                'feedback' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $messageId = $request->input('message_id');
            $rating = $request->input('rating');
            $feedback = $request->input('feedback');

            // Find the message
            $message = Message::findOrFail($messageId);

            // Only allow rating AI messages
            if ($message->type !== 'ai') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only AI responses can be rated'
                ], 400);
            }

            // Find the corresponding user message
            $userMessage = Message::where('conversation_id', $message->conversation_id)
                ->where('type', 'user')
                ->where('created_at', '<', $message->created_at)
                ->orderBy('created_at', 'desc')
                ->first();

            if ($userMessage) {
                // Update quality rating in knowledge base
                $updated = $this->widdxAiService->updateQualityRating(
                    $userMessage->content,
                    $message->language,
                    $rating
                );

                // Update message quality score
                $message->update([
                    'quality_score' => $rating,
                    'metadata' => array_merge($message->metadata ?? [], [
                        'user_feedback' => $feedback,
                        'feedback_timestamp' => now()->toISOString()
                    ])
                ]);

                Log::info('Response rated successfully', [
                    'message_id' => $messageId,
                    'rating' => $rating,
                    'knowledge_base_updated' => $updated
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Rating submitted successfully',
                    'data' => [
                        'rating' => $rating,
                        'knowledge_base_updated' => $updated
                    ]
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Could not find corresponding user message'
            ], 400);

        } catch (Exception $e) {
            Log::error('Failed to rate response', [
                'error' => $e->getMessage(),
                'message_id' => $request->input('message_id')
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to submit rating'
            ], 500);
        }
    }

    /**
     * Get learning statistics
     */
    public function getLearningStats(): JsonResponse
    {
        try {
            $stats = $this->widdxAiService->getLearningStats();

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get learning stats', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve learning statistics'
            ], 500);
        }
    }

    /**
     * Prune knowledge base
     */
    public function pruneKnowledgeBase(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'min_quality' => 'nullable|numeric|min:0|max:1',
                'max_age_days' => 'nullable|integer|min:1',
                'min_usage_count' => 'nullable|integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $options = array_filter([
                'min_quality' => $request->input('min_quality'),
                'max_age_days' => $request->input('max_age_days'),
                'min_usage_count' => $request->input('min_usage_count')
            ]);

            $result = $this->widdxAiService->pruneKnowledgeBase($options);

            return response()->json([
                'success' => true,
                'data' => $result
            ]);

        } catch (Exception $e) {
            Log::error('Failed to prune knowledge base', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to prune knowledge base'
            ], 500);
        }
    }

    /**
     * Get system health status
     */
    public function getHealthStatus(): JsonResponse
    {
        try {
            $health = $this->widdxAiService->getHealthStatus();

            return response()->json([
                'success' => true,
                'data' => $health
            ]);

        } catch (Exception $e) {
            Log::error('Health check failed', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'Health check failed',
                'data' => [
                    'overall_status' => 'error',
                    'timestamp' => now()->toISOString()
                ]
            ], 500);
        }
    }

    /**
     * Find or create conversation
     */
    private function findOrCreateConversation(
        ?string $sessionId,
        string $language,
        Request $request
    ): Conversation {
        if ($sessionId) {
            $conversation = Conversation::where('session_id', $sessionId)->first();
            if ($conversation) {
                return $conversation;
            }
        }

        // Create new conversation
        return Conversation::create([
            'session_id' => $sessionId ?: Str::uuid()->toString(),
            'language' => $language,
            'user_ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'last_activity_at' => now(),
            'metadata' => [
                'created_via' => 'api',
                'initial_language' => $language
            ]
        ]);
    }
}
