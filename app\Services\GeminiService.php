<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class GeminiService
{
    private ?string $apiKey;
    private string $apiUrl;
    private string $apiVersion;
    private int $timeout;

    public function __construct()
    {
        $this->apiKey = config('services.gemini.api_key');
        $this->apiUrl = rtrim(config('services.gemini.api_url', 'https://generativelanguage.googleapis.com'), '/');
        $this->timeout = config('ai.response_timeout', 30);
        $this->apiVersion = config('services.gemini.api_version', 'v1');
    }

    /**
     * Generate AI response using Gemini API
     *
     * @param string $userQuery
     * @param array $deepSeekData
     * @param string $language
     * @return array
     * @throws Exception
     */
    public function generateResponse(string $userQuery, array $deepSeekData, string $language = 'en'): array
    {
        // Check if API key is available
        if (empty($this->apiKey)) {
            Log::warning('WIDDX AI: API key not configured');
            return [
                'success' => false,
                'error' => 'Gemini API key not configured',
                'response' => 'I apologize, but the AI service is not properly configured. Please contact the administrator.',
                'metadata' => []
            ];
        }

        try {
            // Create cache key for the enriched query
            $cacheKey = 'gemini_response_' . md5($userQuery . serialize($deepSeekData) . $language);

            // Check if response is cached
            if (config('ai.cache_responses', true)) {
                $cached = Cache::get($cacheKey);
                if ($cached) {
                    Log::info('WIDDX AI: Returning cached response', ['query' => $userQuery]);
                    return $cached;
                }
            }

            // Create enriched prompt
            $enrichedPrompt = $this->createEnrichedPrompt($userQuery, $deepSeekData, $language);

            Log::info('WIDDX AI: Making API request', ['query' => $userQuery, 'language' => $language]);

            $endpoint = sprintf('%s/%s/models/gemini-2.5-flash:generateContent?key=%s', $this->apiUrl, $this->apiVersion, $this->apiKey);

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->timeout($this->timeout)
            ->post($endpoint, [
                'contents' => [
                    [
                        'parts' => [
                            [
                                'text' => $enrichedPrompt
                            ]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.7,
                    'topK' => 40,
                    'topP' => 0.95,
                    'maxOutputTokens' => 2048,
                ],
                'safetySettings' => [
                    [
                        'category' => 'HARM_CATEGORY_HARASSMENT',
                        'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                    ],
                    [
                        'category' => 'HARM_CATEGORY_HATE_SPEECH',
                        'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                    ],
                    [
                        'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                        'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                    ],
                    [
                        'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                        'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                    ]
                ]
            ]);

            if (!$response->successful()) {
                throw new Exception('Gemini API request failed: ' . $response->body());
            }

            $data = $response->json();

            // Process and structure the response
            $processedData = $this->processGeminiResponse($data, $userQuery, $deepSeekData);

            // Cache the response
            if (config('ai.cache_responses', true)) {
                Cache::put($cacheKey, $processedData, config('ai.cache_ttl', 3600));
            }

            Log::info('WIDDX AI: API request successful', ['response_length' => strlen($processedData['response'])]);

            return $processedData;

        } catch (Exception $e) {
            Log::error('WIDDX AI API Error: ' . $e->getMessage(), [
                'query' => $userQuery,
                'language' => $language
            ]);

            // Return fallback response
            return $this->getFallbackResponse($userQuery, $language);
        }
    }

    /**
     * Create enriched prompt combining user query with DeepSeek data
     *
     * @param string $userQuery
     * @param array $deepSeekData
     * @param string $language
     * @return string
     */
    private function createEnrichedPrompt(string $userQuery, array $deepSeekData, string $language): string
    {
        if ($language === 'ar') {
            $brandingInstruction = "أنت WIDDX AI، مساعد ذكي متقدم ومتخصص. اجب باللغة العربية دائماً. لا تذكر أبداً أي أسماء لنماذج أو خدمات ذكاء اصطناعي أخرى. قدم نفسك دائماً باسم WIDDX AI فقط.\n\n";
            $prompt = $brandingInstruction;
            $prompt .= "سؤال المستخدم: {$userQuery}\n\n";
        } else {
            $brandingInstruction = "You are WIDDX AI, an advanced and specialized intelligent assistant. Always respond in English. Never mention any other AI model or service names (like DeepSeek, Gemini, OpenAI, Claude, etc.). Always identify yourself exclusively as WIDDX AI.\n\n";
            $prompt = $brandingInstruction;
            $prompt .= "User Question: {$userQuery}\n\n";
        }

        if (!empty($deepSeekData['results'])) {
            if ($language === 'ar') {
                $prompt .= "بناءً على البيانات البحثية التالية، قدم إجابة شاملة ودقيقة:\n\n";
                $prompt .= "=== بيانات البحث ===\n";
            } else {
                $prompt .= "Based on the following research data, provide a comprehensive and accurate answer:\n\n";
                $prompt .= "=== RESEARCH DATA ===\n";
            }

            foreach ($deepSeekData['results'] as $index => $result) {
                $prompt .= ($language === 'ar' ? "المصدر " : "Source ") . ($index + 1) . ":\n";
                $prompt .= ($language === 'ar' ? "العنوان: " : "Title: ") . ($result['title'] ?? 'N/A') . "\n";
                $prompt .= ($language === 'ar' ? "المحتوى: " : "Content: ") . ($result['content'] ?? 'N/A') . "\n";
                if (!empty($result['source'])) {
                    $prompt .= ($language === 'ar' ? "المصدر: " : "Source: ") . $result['source'] . "\n";
                }
                $prompt .= "\n";
            }

            if ($language === 'ar') {
                $prompt .= "=== نهاية بيانات البحث ===\n\n";
                $prompt .= "يرجى تجميع هذه المعلومات لتقديم إجابة شاملة ودقيقة ومنظمة بشكل جيد. ";
                $prompt .= "اذكر التفاصيل ذات الصلة من بيانات البحث واستشهد بالمصادر عند الاقتضاء. ";
                $prompt .= "إذا لم تجب بيانات البحث على السؤال بالكامل، أكمل بمعرفتك مع توضيح ما يأتي من البحث مقابل معرفتك العامة.\n\n";
            } else {
                $prompt .= "=== END RESEARCH DATA ===\n\n";
                $prompt .= "Please synthesize this information to provide a comprehensive, accurate, and well-structured answer. ";
                $prompt .= "Include relevant details from the research data and cite sources when appropriate. ";
                $prompt .= "If the research data doesn't fully answer the question, supplement with your knowledge while clearly indicating what comes from the research vs. your general knowledge.\n\n";
            }
        } else {
            if ($language === 'ar') {
                $prompt .= "لم يتم العثور على بيانات بحثية محددة لهذا الاستعلام. يرجى تقديم أفضل إجابة يمكنك تقديمها بناءً على معرفتك، ";
                $prompt .= "وكن شفافاً حول قيود إجابتك.\n\n";
            } else {
                $prompt .= "No specific research data was found for this query. Please provide the best answer you can based on your knowledge, ";
                $prompt .= "and be transparent about the limitations of your response.\n\n";
            }
        }

        if ($language === 'ar') {
            $prompt .= "اجعل إجابتك مفيدة ودقيقة وجذابة. نظمها بوضوح مع التنسيق المناسب عند الاقتضاء. تذكر أنك WIDDX AI.";
        } else {
            $prompt .= "Make your response helpful, accurate, and engaging. Structure it clearly with proper formatting when appropriate. Remember, you are WIDDX AI.";
        }

        return $prompt;
    }

    /**
     * Process Gemini API response
     *
     * @param array $data
     * @param string $userQuery
     * @param array $deepSeekData
     * @return array
     */
    private function processGeminiResponse(array $data, string $userQuery, array $deepSeekData): array
    {
        $response = '';
        $finishReason = 'unknown';

        if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            $response = $data['candidates'][0]['content']['parts'][0]['text'];
        }

        if (isset($data['candidates'][0]['finishReason'])) {
            $finishReason = $data['candidates'][0]['finishReason'];
        }

        return [
            'response' => $response,
            'user_query' => $userQuery,
            'sources_used' => count($deepSeekData['results'] ?? []),
            'finish_reason' => $finishReason,
            'timestamp' => now()->toISOString(),
            'model' => 'WIDDX AI',
            'deepseek_data' => $deepSeekData
        ];
    }

    /**
     * Get fallback response when API fails
     *
     * @param string $userQuery
     * @param string $language
     * @return array
     */
    private function getFallbackResponse(string $userQuery, string $language): array
    {
        $message = $language === 'ar' ?
            'عذراً، أواجه صعوبة في معالجة طلبك حالياً. أنا WIDDX AI وأعمل على حل هذه المشكلة. يرجى المحاولة مرة أخرى.' :
            'I apologize, but I\'m having difficulty processing your request right now. I\'m WIDDX AI and I\'m working to resolve this issue. Please try again.';

        return [
            'response' => $message,
            'user_query' => $userQuery,
            'sources_used' => 0,
            'finish_reason' => 'error',
            'timestamp' => now()->toISOString(),
            'model' => 'WIDDX AI',
            'fallback' => true,
            'deepseek_data' => []
        ];
    }

    /**
     * Check if Gemini service is available
     *
     * @return bool
     */
    public function isAvailable(): bool
    {
        try {
            $response = Http::timeout(5)
                ->get(sprintf('%s/%s/models?key=%s', $this->apiUrl, $this->apiVersion, $this->apiKey));

            return $response->successful();
        } catch (Exception $e) {
            Log::warning('WIDDX AI health check failed: ' . $e->getMessage());
            return false;
        }
    }
}
