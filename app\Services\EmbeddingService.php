<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class EmbeddingService
{
    private string $provider;
    private array $config;

    public function __construct()
    {
        $this->provider = config('widdx.embedding.provider', 'openai');
        $this->config = config('widdx.embedding.providers.' . $this->provider, []);
    }

    /**
     * Generate embedding vector for text
     */
    public function generateEmbedding(string $text, string $language = 'en'): ?array
    {
        try {
            // Normalize text
            $normalizedText = $this->normalizeText($text);
            
            // Check cache first
            $cacheKey = 'embedding:' . hash('sha256', $normalizedText . ':' . $language);
            $cached = Cache::get($cacheKey);
            if ($cached) {
                return $cached;
            }

            $embedding = match ($this->provider) {
                'openai' => $this->generateOpenAIEmbedding($normalizedText),
                'local' => $this->generateLocalEmbedding($normalizedText),
                'sentence_transformer' => $this->generateSentenceTransformerEmbedding($normalizedText),
                default => throw new \Exception('Unsupported embedding provider: ' . $this->provider)
            };

            // Cache for 24 hours
            if ($embedding) {
                Cache::put($cacheKey, $embedding, 86400);
            }

            return $embedding;
        } catch (\Exception $e) {
            Log::error('Embedding generation failed', [
                'text' => substr($text, 0, 100),
                'provider' => $this->provider,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Calculate cosine similarity between two vectors
     */
    public function calculateSimilarity(array $vector1, array $vector2): float
    {
        if (count($vector1) !== count($vector2)) {
            throw new \InvalidArgumentException('Vectors must have the same dimension');
        }

        $dotProduct = 0;
        $magnitude1 = 0;
        $magnitude2 = 0;

        for ($i = 0; $i < count($vector1); $i++) {
            $dotProduct += $vector1[$i] * $vector2[$i];
            $magnitude1 += $vector1[$i] * $vector1[$i];
            $magnitude2 += $vector2[$i] * $vector2[$i];
        }

        $magnitude1 = sqrt($magnitude1);
        $magnitude2 = sqrt($magnitude2);

        if ($magnitude1 == 0 || $magnitude2 == 0) {
            return 0;
        }

        return $dotProduct / ($magnitude1 * $magnitude2);
    }

    /**
     * Find most similar embeddings from a collection
     */
    public function findMostSimilar(array $queryVector, array $candidateVectors, float $threshold = 0.7): array
    {
        $similarities = [];

        foreach ($candidateVectors as $id => $vector) {
            $similarity = $this->calculateSimilarity($queryVector, $vector);
            if ($similarity >= $threshold) {
                $similarities[$id] = $similarity;
            }
        }

        // Sort by similarity descending
        arsort($similarities);
        return $similarities;
    }

    /**
     * Generate embedding using OpenAI API
     */
    private function generateOpenAIEmbedding(string $text): ?array
    {
        $apiKey = $this->config['api_key'] ?? env('OPENAI_API_KEY');
        if (!$apiKey) {
            throw new \Exception('OpenAI API key not configured');
        }

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $apiKey,
            'Content-Type' => 'application/json',
        ])->timeout(30)->post('https://api.openai.com/v1/embeddings', [
            'model' => $this->config['model'] ?? 'text-embedding-3-small',
            'input' => $text,
            'encoding_format' => 'float'
        ]);

        if ($response->successful()) {
            $data = $response->json();
            return $data['data'][0]['embedding'] ?? null;
        }

        throw new \Exception('OpenAI API request failed: ' . $response->body());
    }

    /**
     * Generate embedding using local model (placeholder)
     */
    private function generateLocalEmbedding(string $text): ?array
    {
        // This is a placeholder for local embedding generation
        // You could integrate with libraries like sentence-transformers via Python
        // or use a local API endpoint
        
        // For now, return a simple hash-based vector (not recommended for production)
        $hash = hash('sha256', $text);
        $vector = [];
        for ($i = 0; $i < 384; $i += 8) {
            $vector[] = hexdec(substr($hash, $i % 64, 8)) / 4294967295; // Normalize to 0-1
        }
        return array_slice($vector, 0, 384);
    }

    /**
     * Generate embedding using sentence transformer API
     */
    private function generateSentenceTransformerEmbedding(string $text): ?array
    {
        $endpoint = $this->config['endpoint'] ?? 'http://localhost:8080/embed';
        
        $response = Http::timeout(30)->post($endpoint, [
            'text' => $text,
            'model' => $this->config['model'] ?? 'all-MiniLM-L6-v2'
        ]);

        if ($response->successful()) {
            $data = $response->json();
            return $data['embedding'] ?? null;
        }

        throw new \Exception('Sentence Transformer API request failed: ' . $response->body());
    }

    /**
     * Normalize text for consistent embedding generation
     */
    private function normalizeText(string $text): string
    {
        // Remove extra whitespace
        $text = preg_replace('/\s+/', ' ', trim($text));
        
        // Convert to lowercase for consistency
        $text = strtolower($text);
        
        // Remove special characters that don't add semantic meaning
        $text = preg_replace('/[^\w\s\?\!\.]/', '', $text);
        
        return $text;
    }

    /**
     * Get embedding dimension for the current provider
     */
    public function getEmbeddingDimension(): int
    {
        return match ($this->provider) {
            'openai' => match ($this->config['model'] ?? 'text-embedding-3-small') {
                'text-embedding-3-small' => 1536,
                'text-embedding-3-large' => 3072,
                'text-embedding-ada-002' => 1536,
                default => 1536
            },
            'local' => 384,
            'sentence_transformer' => $this->config['dimension'] ?? 384,
            default => 384
        };
    }

    /**
     * Batch generate embeddings for multiple texts
     */
    public function batchGenerateEmbeddings(array $texts, string $language = 'en'): array
    {
        $embeddings = [];
        
        foreach ($texts as $index => $text) {
            $embedding = $this->generateEmbedding($text, $language);
            if ($embedding) {
                $embeddings[$index] = $embedding;
            }
        }
        
        return $embeddings;
    }
}