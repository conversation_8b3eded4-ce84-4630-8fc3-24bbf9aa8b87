/**
 * WIDDX AI - Elite Theme Manager
 * Advanced Theme Management and UI State Control
 */

class WiddxThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.themeToggle = null;
        this.themeIcon = null;
        
        this.initializeElements();
        this.initializeTheme();
        this.bindEvents();
    }

    initializeElements() {
        this.themeToggle = document.getElementById('themeToggle');
        this.themeIcon = document.getElementById('themeIcon');
        
        if (!this.themeToggle || !this.themeIcon) {
            console.warn('Theme toggle elements not found');
            return;
        }
    }

    bindEvents() {
        if (this.themeToggle) {
            this.themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Listen for system theme changes
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', (e) => {
                if (!this.hasUserPreference()) {
                    this.setTheme(e.matches ? 'dark' : 'light', false);
                }
            });
        }

        // Listen for storage changes (for multi-tab sync)
        window.addEventListener('storage', (e) => {
            if (e.key === 'widdx-theme') {
                this.setTheme(e.newValue || 'light', false);
            }
        });
    }

    initializeTheme() {
        const savedTheme = this.getSavedTheme();
        const systemTheme = this.getSystemTheme();
        const initialTheme = savedTheme || systemTheme;
        
        this.setTheme(initialTheme, false);
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme, true);
        
        // Add a subtle animation effect
        this.addToggleAnimation();
    }

    setTheme(theme, savePreference = true) {
        if (!['light', 'dark'].includes(theme)) {
            theme = 'light';
        }

        this.currentTheme = theme;
        
        // Update DOM
        this.updateDOM(theme);
        
        // Update icon
        this.updateIcon(theme);
        
        // Save preference
        if (savePreference) {
            this.saveTheme(theme);
        }
        
        // Dispatch theme change event
        this.dispatchThemeChangeEvent(theme);
    }

    updateDOM(theme) {
        const body = document.body;
        
        if (theme === 'dark') {
            body.setAttribute('data-theme', 'dark');
        } else {
            body.removeAttribute('data-theme');
        }
        
        // Add transition class for smooth theme switching
        body.classList.add('theme-transitioning');
        
        setTimeout(() => {
            body.classList.remove('theme-transitioning');
        }, 300);
    }

    updateIcon(theme) {
        if (!this.themeIcon) return;
        
        if (theme === 'dark') {
            this.themeIcon.className = 'bi bi-sun-fill';
            this.themeToggle.setAttribute('title', 'Switch to light mode');
        } else {
            this.themeIcon.className = 'bi bi-moon-fill';
            this.themeToggle.setAttribute('title', 'Switch to dark mode');
        }
    }

    addToggleAnimation() {
        if (!this.themeToggle) return;
        
        this.themeToggle.classList.add('theme-toggle-active');
        
        setTimeout(() => {
            this.themeToggle.classList.remove('theme-toggle-active');
        }, 300);
    }

    getSavedTheme() {
        try {
            return localStorage.getItem('widdx-theme');
        } catch (error) {
            console.warn('Unable to access localStorage for theme preference');
            return null;
        }
    }

    saveTheme(theme) {
        try {
            localStorage.setItem('widdx-theme', theme);
        } catch (error) {
            console.warn('Unable to save theme preference to localStorage');
        }
    }

    getSystemTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }

    hasUserPreference() {
        return this.getSavedTheme() !== null;
    }

    dispatchThemeChangeEvent(theme) {
        const event = new CustomEvent('themeChange', {
            detail: { theme }
        });
        window.dispatchEvent(event);
    }

    // Public API methods
    getCurrentTheme() {
        return this.currentTheme;
    }

    isDarkMode() {
        return this.currentTheme === 'dark';
    }

    isLightMode() {
        return this.currentTheme === 'light';
    }

    // Force theme without saving preference (useful for demos)
    forceTheme(theme) {
        this.setTheme(theme, false);
    }

    // Reset to system preference
    resetToSystemTheme() {
        try {
            localStorage.removeItem('widdx-theme');
        } catch (error) {
            console.warn('Unable to remove theme preference from localStorage');
        }
        
        const systemTheme = this.getSystemTheme();
        this.setTheme(systemTheme, false);
    }

    // Get theme-aware color values
    getThemeColor(colorName) {
        const root = document.documentElement;
        const computedStyle = getComputedStyle(root);
        return computedStyle.getPropertyValue(`--${colorName}`).trim();
    }

    // Update CSS custom properties dynamically
    updateThemeColor(colorName, colorValue) {
        const root = document.documentElement;
        root.style.setProperty(`--${colorName}`, colorValue);
    }

    // Theme-aware utility methods
    getContrastColor(backgroundColor) {
        // Simple contrast calculation
        const rgb = this.hexToRgb(backgroundColor);
        if (!rgb) return this.currentTheme === 'dark' ? '#ffffff' : '#000000';
        
        const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
        return brightness > 128 ? '#000000' : '#ffffff';
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    // Animation utilities
    addThemeTransition(element, duration = 300) {
        if (!element) return;
        
        element.style.transition = `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1)`;
        
        setTimeout(() => {
            element.style.transition = '';
        }, duration);
    }

    // Accessibility helpers
    respectsReducedMotion() {
        return window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    }

    respectsHighContrast() {
        return window.matchMedia && window.matchMedia('(prefers-contrast: high)').matches;
    }

    // Debug methods
    logThemeInfo() {
        console.log('WIDDX Theme Manager Info:', {
            currentTheme: this.currentTheme,
            savedTheme: this.getSavedTheme(),
            systemTheme: this.getSystemTheme(),
            hasUserPreference: this.hasUserPreference(),
            reducedMotion: this.respectsReducedMotion(),
            highContrast: this.respectsHighContrast()
        });
    }
}

// Global theme management functions
function toggleTheme() {
    if (window.widdxThemeManager) {
        window.widdxThemeManager.toggleTheme();
    }
}

function getCurrentTheme() {
    return window.widdxThemeManager ? window.widdxThemeManager.getCurrentTheme() : 'light';
}

function setTheme(theme) {
    if (window.widdxThemeManager) {
        window.widdxThemeManager.setTheme(theme, true);
    }
}

// Export for use in other modules
window.WiddxThemeManager = WiddxThemeManager;
window.toggleTheme = toggleTheme;
window.getCurrentTheme = getCurrentTheme;
window.setTheme = setTheme;
