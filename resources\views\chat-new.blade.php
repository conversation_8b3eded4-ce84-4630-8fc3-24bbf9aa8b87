<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#ffffff">
    <meta name="description" content="WIDDX AI - Elite Chat Interface with Advanced AI Assistant">
    <title>WIDDX AI Chat</title>
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="{{ asset('css/variables.css') }}" as="style">
    <link rel="preload" href="{{ asset('css/base.css') }}" as="style">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Elite CSS Architecture -->
    <link rel="stylesheet" href="{{ asset('css/variables.css') }}">
    <link rel="stylesheet" href="{{ asset('css/base.css') }}">
    <link rel="stylesheet" href="{{ asset('css/themes.css') }}">
    <link rel="stylesheet" href="{{ asset('css/layout.css') }}">
    <link rel="stylesheet" href="{{ asset('css/components.css') }}">
    <link rel="stylesheet" href="{{ asset('css/messages.css') }}">
    <link rel="stylesheet" href="{{ asset('css/states.css') }}">
    <link rel="stylesheet" href="{{ asset('css/responsive.css') }}">
</head>
<body>
    <!-- WIDDX AI Elite Chat Interface -->
    <div class="chat-layout">
        <!-- Elite Sidebar Component -->
        @include('components.sidebar')

        <!-- Main Chat Area -->
        <div class="main-chat">
            <!-- Elite Chat Header Component -->
            @include('components.chat-header', ['title' => 'Welcome to WIDDX AI'])

            <!-- Elite Messages Container Component -->
            @include('components.messages-container', [
                'welcomeTitle' => 'Welcome to WIDDX AI',
                'welcomeMessage' => 'Your intelligent assistant is ready to help. Start a conversation by typing a message below.'
            ])

            <!-- Elite Typing Indicator Component -->
            @include('components.typing-indicator', ['message' => 'WIDDX AI is thinking'])

            <!-- Elite Chat Input Component -->
            @include('components.chat-input', [
                'placeholder' => 'Type your message here...',
                'maxLength' => 4000
            ])
        </div>
    </div>

    <!-- Elite JavaScript Architecture -->
    <script src="{{ asset('js/theme-manager.js') }}" defer></script>
    <script src="{{ asset('js/ui-manager.js') }}" defer></script>
    <script src="{{ asset('js/chat-core.js') }}" defer></script>
    <script src="{{ asset('js/app.js') }}" defer></script>
</body>
</html>
