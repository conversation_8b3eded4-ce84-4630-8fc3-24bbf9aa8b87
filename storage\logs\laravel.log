[2025-08-10 22:44:05] local.INFO: WIDDX AI: Processing query {"query":"test after gemini v1 endpoint","language":"en","session_id":"06c76603-4625-4514-87a9-1afd7070d691","knowledge_base_enabled":true} 
[2025-08-10 22:44:05] local.INFO: DeepSeek: Making API request {"query":"test after gemini v1 endpoint","language":"en"} 
[2025-08-10 22:44:06] local.ERROR: DeepSeek API Error: DeepSeek API request failed: {
  "error_msg": "Not Found. Please check the configuration."
} {"query":"test after gemini v1 endpoint","language":"en"} 
[2025-08-10 22:44:06] local.INFO: Gemini: Making API request {"query":"test after gemini v1 endpoint","language":"en"} 
[2025-08-10 22:44:06] local.ERROR: Gemini API Error: Gemini API request failed: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}
 {"query":"test after gemini v1 endpoint","language":"en"} 
[2025-08-10 22:44:06] local.INFO: New knowledge learned {"question":"test after gemini v1 endpoint","language":"en","source_model":"external_api","confidence":0.2,"knowledge_id":17} 
[2025-08-10 22:44:06] local.INFO: WIDDX AI: Query processed successfully {"session_id":"06c76603-4625-4514-87a9-1afd7070d691","processing_time":1421.73,"sources_used":0} 
[2025-08-10 22:44:06] local.INFO: Chat message processed successfully {"session_id":"06c76603-4625-4514-87a9-1afd7070d691","user_message_id":11,"ai_message_id":12,"processing_time":1421.73} 
[2025-08-10 22:51:09] local.INFO: WIDDX AI: Processing query {"query":"مرحبا","language":"en","session_id":"afd4ea98-c8d0-4108-be2c-97779a297db0","knowledge_base_enabled":true} 
[2025-08-10 22:51:09] local.INFO: DeepSeek: Making API request {"query":"مرحبا","language":"en"} 
[2025-08-10 22:51:11] local.ERROR: DeepSeek API Error: DeepSeek API request failed: {
  "error_msg": "Not Found. Please check the configuration."
} {"query":"مرحبا","language":"en"} 
[2025-08-10 22:51:11] local.INFO: Gemini: Making API request {"query":"مرحبا","language":"en"} 
[2025-08-10 22:51:12] local.ERROR: Gemini API Error: Gemini API request failed: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}
 {"query":"مرحبا","language":"en"} 
[2025-08-10 22:51:12] local.INFO: New knowledge learned {"question":"مرحبا","language":"en","source_model":"external_api","confidence":0.2,"knowledge_id":18} 
[2025-08-10 22:51:12] local.INFO: WIDDX AI: Query processed successfully {"session_id":"afd4ea98-c8d0-4108-be2c-97779a297db0","processing_time":2848.8,"sources_used":0} 
[2025-08-10 22:51:12] local.INFO: Chat message processed successfully {"session_id":"afd4ea98-c8d0-4108-be2c-97779a297db0","user_message_id":13,"ai_message_id":14,"processing_time":2848.8} 
[2025-08-10 22:55:54] local.INFO: WIDDX AI: Processing query {"query":"deepseek now integrated, give a short one-line response","language":"en","session_id":"cc438334-2b70-45f0-ba2b-6cf2a864219e","knowledge_base_enabled":true} 
[2025-08-10 22:55:54] local.INFO: DeepSeek: Making API request {"query":"deepseek now integrated, give a short one-line response","language":"en"} 
[2025-08-10 22:55:58] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-10 22:55:58] local.INFO: Gemini: Making API request {"query":"deepseek now integrated, give a short one-line response","language":"en"} 
[2025-08-10 22:55:58] local.ERROR: Gemini API Error: Gemini API request failed: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}
 {"query":"deepseek now integrated, give a short one-line response","language":"en"} 
[2025-08-10 22:55:58] local.INFO: New knowledge learned {"question":"deepseek now integrated, give a short one-line response","language":"en","source_model":"external_api","confidence":0.54,"knowledge_id":19} 
[2025-08-10 22:55:58] local.INFO: WIDDX AI: Query processed successfully {"session_id":"cc438334-2b70-45f0-ba2b-6cf2a864219e","processing_time":4242.59,"sources_used":1} 
[2025-08-10 22:55:58] local.INFO: Chat message processed successfully {"session_id":"cc438334-2b70-45f0-ba2b-6cf2a864219e","user_message_id":15,"ai_message_id":16,"processing_time":4242.59} 
[2025-08-10 22:56:38] local.INFO: WIDDX AI: Processing query {"query":"مرحبا","language":"en","session_id":"30b82747-5915-444e-a37d-9d4a398d773b","knowledge_base_enabled":true} 
[2025-08-10 22:56:38] local.INFO: DeepSeek: Making API request {"query":"مرحبا","language":"en"} 
[2025-08-10 22:56:42] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-10 22:56:42] local.INFO: Gemini: Making API request {"query":"مرحبا","language":"en"} 
[2025-08-10 22:56:42] local.ERROR: Gemini API Error: Gemini API request failed: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}
 {"query":"مرحبا","language":"en"} 
[2025-08-10 22:56:42] local.INFO: WIDDX AI: Query processed successfully {"session_id":"30b82747-5915-444e-a37d-9d4a398d773b","processing_time":4398.06,"sources_used":1} 
[2025-08-10 22:56:42] local.INFO: Chat message processed successfully {"session_id":"30b82747-5915-444e-a37d-9d4a398d773b","user_message_id":17,"ai_message_id":18,"processing_time":4398.06} 
[2025-08-10 22:57:40] local.INFO: WIDDX AI: Processing query {"query":"?????","language":"ar","session_id":"08e99c91-d2e5-4f0c-a10a-a542d1fd6bc3","knowledge_base_enabled":true} 
[2025-08-10 22:57:40] local.INFO: DeepSeek: Making API request {"query":"?????","language":"ar"} 
[2025-08-10 22:57:47] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-10 22:57:47] local.INFO: Gemini: Making API request {"query":"?????","language":"ar"} 
[2025-08-10 22:57:47] local.ERROR: Gemini API Error: Gemini API request failed: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}
 {"query":"?????","language":"ar"} 
[2025-08-10 22:57:47] local.INFO: New knowledge learned {"question":"?????","language":"ar","source_model":"external_api","confidence":1.0,"knowledge_id":20} 
[2025-08-10 22:57:47] local.INFO: WIDDX AI: Query processed successfully {"session_id":"08e99c91-d2e5-4f0c-a10a-a542d1fd6bc3","processing_time":7816.47,"sources_used":1} 
[2025-08-10 22:57:47] local.INFO: Chat message processed successfully {"session_id":"08e99c91-d2e5-4f0c-a10a-a542d1fd6bc3","user_message_id":19,"ai_message_id":20,"processing_time":7816.47} 
[2025-08-10 23:03:16] local.INFO: WIDDX AI: Processing query {"query":"???? ???? ??????? ????? ?? ?????","language":"ar","session_id":"e4283d84-f197-4e79-a13c-aba981827af1","knowledge_base_enabled":true} 
[2025-08-10 23:03:16] local.INFO: Found similar question match {"question":"???? ???? ??????? ????? ?? ?????","knowledge_id":20,"similarity":0.7562225762333735} 
[2025-08-10 23:03:16] local.INFO: Knowledge base hit {"question":"???? ???? ??????? ????? ?? ?????","knowledge_id":20,"confidence":1.0,"usage_count":3} 
[2025-08-10 23:03:16] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"e4283d84-f197-4e79-a13c-aba981827af1","confidence":1.0,"source":"knowledge_base"} 
[2025-08-10 23:03:16] local.INFO: Chat message processed successfully {"session_id":"e4283d84-f197-4e79-a13c-aba981827af1","user_message_id":21,"ai_message_id":22,"processing_time":36.45} 
[2025-08-10 23:03:56] local.INFO: WIDDX AI: Processing query {"query":"مرحبا بك","language":"en","session_id":"effd2fb2-2f95-4458-8d89-74c9217d3149","knowledge_base_enabled":true} 
[2025-08-10 23:03:56] local.INFO: DeepSeek: Making API request {"query":"مرحبا بك","language":"en"} 
[2025-08-10 23:04:00] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-10 23:04:00] local.INFO: Gemini: Making API request {"query":"مرحبا بك","language":"en"} 
[2025-08-10 23:04:00] local.ERROR: Gemini API Error: Gemini API request failed: {
  "error": {
    "code": 404,
    "message": "models/gemini-pro is not found for API version v1, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.",
    "status": "NOT_FOUND"
  }
}
 {"query":"مرحبا بك","language":"en"} 
[2025-08-10 23:04:00] local.INFO: New knowledge learned {"question":"مرحبا بك","language":"en","source_model":"external_api","confidence":1.0,"knowledge_id":21} 
[2025-08-10 23:04:00] local.INFO: WIDDX AI: Query processed successfully {"session_id":"effd2fb2-2f95-4458-8d89-74c9217d3149","processing_time":4494.94,"sources_used":1} 
[2025-08-10 23:04:00] local.INFO: Chat message processed successfully {"session_id":"effd2fb2-2f95-4458-8d89-74c9217d3149","user_message_id":23,"ai_message_id":24,"processing_time":4494.94} 
[2025-08-10 23:10:51] local.INFO: WIDDX AI: Processing query {"query":"???? ???? ??????? ????? ?? ?????","language":"ar","session_id":"db42493c-882e-4085-9f8d-5ea1f1bad82c","knowledge_base_enabled":true} 
[2025-08-10 23:10:51] local.INFO: Found similar question match {"question":"???? ???? ??????? ????? ?? ?????","knowledge_id":20,"similarity":0.7562225762333735} 
[2025-08-10 23:10:51] local.INFO: Knowledge base hit {"question":"???? ???? ??????? ????? ?? ?????","knowledge_id":20,"confidence":1.0,"usage_count":4} 
[2025-08-10 23:10:51] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"db42493c-882e-4085-9f8d-5ea1f1bad82c","confidence":1.0,"source":"knowledge_base"} 
[2025-08-10 23:10:51] local.INFO: Chat message processed successfully {"session_id":"db42493c-882e-4085-9f8d-5ea1f1bad82c","user_message_id":25,"ai_message_id":26,"processing_time":41.78} 
[2025-08-10 23:11:15] local.INFO: WIDDX AI: Processing query {"query":"What is artificial intelligence in simple terms?","language":"en","session_id":"b7dd30d9-02fd-4e03-a846-4f6af3900367","knowledge_base_enabled":true} 
[2025-08-10 23:11:15] local.INFO: Found similar question match {"question":"What is artificial intelligence in simple terms?","knowledge_id":21,"similarity":0.7789914667321988} 
[2025-08-10 23:11:15] local.INFO: Knowledge base hit {"question":"What is artificial intelligence in simple terms?","knowledge_id":21,"confidence":1.0,"usage_count":3} 
[2025-08-10 23:11:15] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"b7dd30d9-02fd-4e03-a846-4f6af3900367","confidence":1.0,"source":"knowledge_base"} 
[2025-08-10 23:11:15] local.INFO: Chat message processed successfully {"session_id":"b7dd30d9-02fd-4e03-a846-4f6af3900367","user_message_id":27,"ai_message_id":28,"processing_time":21.19} 
[2025-08-10 23:11:21] local.INFO: WIDDX AI: Processing query {"query":"Tell me about quantum computing in 2025","language":"en","session_id":"f9afa1e5-dc66-4004-b700-c5e616c2664a","knowledge_base_enabled":true} 
[2025-08-10 23:11:21] local.INFO: Found similar question match {"question":"Tell me about quantum computing in 2025","knowledge_id":21,"similarity":0.8063661325625655} 
[2025-08-10 23:11:21] local.INFO: Knowledge base hit {"question":"Tell me about quantum computing in 2025","knowledge_id":21,"confidence":1.0,"usage_count":4} 
[2025-08-10 23:11:21] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"f9afa1e5-dc66-4004-b700-c5e616c2664a","confidence":1.0,"source":"knowledge_base"} 
[2025-08-10 23:11:21] local.INFO: Chat message processed successfully {"session_id":"f9afa1e5-dc66-4004-b700-c5e616c2664a","user_message_id":29,"ai_message_id":30,"processing_time":19.97} 
[2025-08-10 23:12:10] local.INFO: WIDDX AI: Processing query {"query":"Tell me about quantum computing in 2025","language":"en","session_id":"4a4b55bf-173c-4ce5-aca0-1575e563faef","knowledge_base_enabled":true} 
[2025-08-10 23:12:10] local.INFO: Found similar question match {"question":"Tell me about quantum computing in 2025","knowledge_id":21,"similarity":0.8063661325625655} 
[2025-08-10 23:12:10] local.INFO: Knowledge base hit {"question":"Tell me about quantum computing in 2025","knowledge_id":21,"confidence":1.0,"usage_count":5} 
[2025-08-10 23:12:10] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"4a4b55bf-173c-4ce5-aca0-1575e563faef","confidence":1.0,"source":"knowledge_base"} 
[2025-08-10 23:12:10] local.INFO: Chat message processed successfully {"session_id":"4a4b55bf-173c-4ce5-aca0-1575e563faef","user_message_id":31,"ai_message_id":32,"processing_time":28.93} 
[2025-08-10 23:14:19] local.INFO: WIDDX AI: Processing query {"query":"Tell me about quantum computing in 2025","language":"en","session_id":"69c287a6-00b2-4618-9341-47aff46f53e7","knowledge_base_enabled":false} 
[2025-08-10 23:14:19] local.INFO: DeepSeek: Making API request {"query":"Tell me about quantum computing in 2025","language":"en"} 
[2025-08-10 23:14:32] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-10 23:14:32] local.INFO: Gemini: Making API request {"query":"Tell me about quantum computing in 2025","language":"en"} 
[2025-08-10 23:14:38] local.INFO: Gemini: API request successful {"response_length":2796} 
[2025-08-10 23:14:38] local.INFO: New knowledge learned {"question":"Tell me about quantum computing in 2025","language":"en","source_model":"external_api","confidence":1.0,"knowledge_id":22} 
[2025-08-10 23:14:38] local.INFO: WIDDX AI: Query processed successfully {"session_id":"69c287a6-00b2-4618-9341-47aff46f53e7","processing_time":18719.67,"sources_used":1} 
[2025-08-10 23:14:38] local.INFO: Chat message processed successfully {"session_id":"69c287a6-00b2-4618-9341-47aff46f53e7","user_message_id":33,"ai_message_id":34,"processing_time":18719.67} 
[2025-08-10 23:14:45] local.INFO: WIDDX AI: Processing query {"query":"???? ???? ??????? ????? ?? ?????? ?????????","language":"ar","session_id":"916ea366-504b-4a84-b3ab-859ed5183463","knowledge_base_enabled":false} 
[2025-08-10 23:14:45] local.INFO: DeepSeek: Making API request {"query":"???? ???? ??????? ????? ?? ?????? ?????????","language":"ar"} 
[2025-08-10 23:14:57] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-10 23:14:57] local.INFO: Gemini: Making API request {"query":"???? ???? ??????? ????? ?? ?????? ?????????","language":"ar"} 
[2025-08-10 23:15:02] local.INFO: Gemini: API request successful {"response_length":2736} 
[2025-08-10 23:15:02] local.INFO: New knowledge learned {"question":"???? ???? ??????? ????? ?? ?????? ?????????","language":"ar","source_model":"external_api","confidence":1.0,"knowledge_id":23} 
[2025-08-10 23:15:02] local.INFO: WIDDX AI: Query processed successfully {"session_id":"916ea366-504b-4a84-b3ab-859ed5183463","processing_time":17286.69,"sources_used":1} 
[2025-08-10 23:15:02] local.INFO: Chat message processed successfully {"session_id":"916ea366-504b-4a84-b3ab-859ed5183463","user_message_id":35,"ai_message_id":36,"processing_time":17286.69} 
[2025-08-10 23:22:00] local.INFO: WIDDX AI: Processing query {"query":"Hello, test message","language":"en","session_id":"7a48af4c-135c-42fa-8455-eeef4c3c9662","knowledge_base_enabled":false} 
[2025-08-10 23:22:00] local.INFO: DeepSeek: Making API request {"query":"Hello, test message","language":"en"} 
[2025-08-10 23:22:04] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-10 23:22:04] local.INFO: Gemini: Making API request {"query":"Hello, test message","language":"en"} 
[2025-08-10 23:22:06] local.INFO: Gemini: API request successful {"response_length":422} 
[2025-08-10 23:22:06] local.INFO: New knowledge learned {"question":"Hello, test message","language":"en","source_model":"external_api","confidence":1.0,"knowledge_id":24} 
[2025-08-10 23:22:06] local.INFO: WIDDX AI: Query processed successfully {"session_id":"7a48af4c-135c-42fa-8455-eeef4c3c9662","processing_time":6777.43,"sources_used":1} 
[2025-08-10 23:22:06] local.INFO: Chat message processed successfully {"session_id":"7a48af4c-135c-42fa-8455-eeef4c3c9662","user_message_id":37,"ai_message_id":38,"processing_time":6777.43} 
[2025-08-10 23:22:14] local.INFO: WIDDX AI: Processing query {"query":"?????? ??? ?????","language":"ar","session_id":"fab23cbe-ebd1-46cb-8f1a-870dc9be0bf1","knowledge_base_enabled":false} 
[2025-08-10 23:22:14] local.INFO: DeepSeek: Making API request {"query":"?????? ??? ?????","language":"ar"} 
[2025-08-10 23:22:18] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-10 23:22:18] local.INFO: Gemini: Making API request {"query":"?????? ??? ?????","language":"ar"} 
[2025-08-10 23:22:29] local.INFO: Gemini: API request successful {"response_length":0} 
[2025-08-10 23:22:29] local.INFO: New knowledge learned {"question":"?????? ??? ?????","language":"ar","source_model":"external_api","confidence":0.54,"knowledge_id":25} 
[2025-08-10 23:22:29] local.INFO: WIDDX AI: Query processed successfully {"session_id":"fab23cbe-ebd1-46cb-8f1a-870dc9be0bf1","processing_time":15014.9,"sources_used":1} 
[2025-08-10 23:22:29] local.INFO: Chat message processed successfully {"session_id":"fab23cbe-ebd1-46cb-8f1a-870dc9be0bf1","user_message_id":39,"ai_message_id":40,"processing_time":15014.9} 
[2025-08-10 23:24:38] local.INFO: WIDDX AI: Processing query {"query":"🎉 TESTING THE FIX - Hello WIDDX AI!","language":"en","session_id":"3d8fd695-18b2-49e8-be36-3804396720b2","knowledge_base_enabled":false} 
[2025-08-10 23:24:38] local.INFO: DeepSeek: Making API request {"query":"🎉 TESTING THE FIX - Hello WIDDX AI!","language":"en"} 
[2025-08-10 23:24:43] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-10 23:24:43] local.INFO: Gemini: Making API request {"query":"🎉 TESTING THE FIX - Hello WIDDX AI!","language":"en"} 
[2025-08-10 23:24:47] local.INFO: Gemini: API request successful {"response_length":576} 
[2025-08-10 23:24:47] local.INFO: New knowledge learned {"question":"🎉 TESTING THE FIX - Hello WIDDX AI!","language":"en","source_model":"external_api","confidence":1.0,"knowledge_id":26} 
[2025-08-10 23:24:47] local.INFO: WIDDX AI: Query processed successfully {"session_id":"3d8fd695-18b2-49e8-be36-3804396720b2","processing_time":9365.15,"sources_used":1} 
[2025-08-10 23:24:47] local.INFO: Chat message processed successfully {"session_id":"3d8fd695-18b2-49e8-be36-3804396720b2","user_message_id":41,"ai_message_id":42,"processing_time":9365.15} 
[2025-08-10 23:28:28] local.INFO: WIDDX AI: Processing query {"query":"اختبار جديد - هل تعمل الآن؟","language":"ar","session_id":"1ef0acf6-2360-43d3-81a6-5ef23867f7f2","knowledge_base_enabled":false} 
[2025-08-10 23:28:28] local.INFO: DeepSeek: Making API request {"query":"اختبار جديد - هل تعمل الآن؟","language":"ar"} 
[2025-08-10 23:28:32] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-10 23:28:32] local.INFO: Gemini: Making API request {"query":"اختبار جديد - هل تعمل الآن؟","language":"ar"} 
[2025-08-10 23:28:35] local.INFO: Gemini: API request successful {"response_length":127} 
[2025-08-10 23:28:35] local.INFO: New knowledge learned {"question":"اختبار جديد - هل تعمل الآن؟","language":"ar","source_model":"external_api","confidence":1.0,"knowledge_id":27} 
[2025-08-10 23:28:35] local.INFO: WIDDX AI: Query processed successfully {"session_id":"1ef0acf6-2360-43d3-81a6-5ef23867f7f2","processing_time":6781.97,"sources_used":1} 
[2025-08-10 23:28:35] local.INFO: Chat message processed successfully {"session_id":"1ef0acf6-2360-43d3-81a6-5ef23867f7f2","user_message_id":43,"ai_message_id":44,"processing_time":6781.97} 
[2025-08-10 23:30:44] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 1 at C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php use Illum...', false)
#2 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('use Illuminate\\\\...', true)
#4 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('use Illuminate\\\\...', true)
#5 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('use Illuminate\\\\...')
#6 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
[2025-08-10 23:31:38] local.INFO: WIDDX AI: Processing query {"query":"?????? ??? ???? ??????","language":"ar","session_id":"bccbe932-8ae4-431c-9b56-05c5aa96a27b","knowledge_base_enabled":true} 
[2025-08-10 23:31:38] local.INFO: DeepSeek: Making API request {"query":"?????? ??? ???? ??????","language":"ar"} 
[2025-08-10 23:31:44] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-10 23:31:44] local.INFO: Gemini: Making API request {"query":"?????? ??? ???? ??????","language":"ar"} 
[2025-08-10 23:31:51] local.INFO: Gemini: API request successful {"response_length":1171} 
[2025-08-10 23:31:51] local.INFO: New knowledge learned {"question":"?????? ??? ???? ??????","language":"ar","source_model":"external_api","confidence":1.0,"knowledge_id":28} 
[2025-08-10 23:31:51] local.INFO: WIDDX AI: Query processed successfully {"session_id":"bccbe932-8ae4-431c-9b56-05c5aa96a27b","processing_time":12611.83,"sources_used":1} 
[2025-08-10 23:31:51] local.INFO: Chat message processed successfully {"session_id":"bccbe932-8ae4-431c-9b56-05c5aa96a27b","user_message_id":45,"ai_message_id":46,"processing_time":12611.83} 
[2025-08-10 23:34:31] local.INFO: WIDDX AI: Processing query {"query":"Hello, this is a test message in English","language":"en","session_id":"5aa1b631-1900-4762-9918-37b3cc4131fe","knowledge_base_enabled":true} 
[2025-08-10 23:34:32] local.INFO: Found similar question match {"question":"Hello, this is a test message in English","knowledge_id":21,"similarity":0.8900281524038521} 
[2025-08-10 23:34:32] local.INFO: Knowledge base hit {"question":"Hello, this is a test message in English","knowledge_id":21,"confidence":1.0,"usage_count":6} 
[2025-08-10 23:34:32] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"5aa1b631-1900-4762-9918-37b3cc4131fe","confidence":1.0,"source":"knowledge_base"} 
[2025-08-10 23:34:32] local.INFO: Chat message processed successfully {"session_id":"5aa1b631-1900-4762-9918-37b3cc4131fe","user_message_id":47,"ai_message_id":48,"processing_time":99.04} 
[2025-08-10 23:34:41] local.INFO: WIDDX AI: Processing query {"query":"????? ???????","language":"ar","session_id":"9e11d9b9-29eb-426b-ac98-eeb3b36b4a09","knowledge_base_enabled":true} 
[2025-08-10 23:34:41] local.INFO: DeepSeek: Making API request {"query":"????? ???????","language":"ar"} 
[2025-08-10 23:34:46] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-10 23:34:46] local.INFO: Gemini: Making API request {"query":"????? ???????","language":"ar"} 
[2025-08-10 23:34:58] local.INFO: Gemini: API request successful {"response_length":0} 
[2025-08-10 23:34:58] local.INFO: New knowledge learned {"question":"????? ???????","language":"ar","source_model":"external_api","confidence":0.54,"knowledge_id":29} 
[2025-08-10 23:34:58] local.INFO: WIDDX AI: Query processed successfully {"session_id":"9e11d9b9-29eb-426b-ac98-eeb3b36b4a09","processing_time":17188.99,"sources_used":1} 
[2025-08-10 23:34:58] local.INFO: Chat message processed successfully {"session_id":"9e11d9b9-29eb-426b-ac98-eeb3b36b4a09","user_message_id":49,"ai_message_id":50,"processing_time":17188.99} 
[2025-08-10 23:35:55] local.INFO: Raw message input {"message":"?????? ???????","length":14,"encoding":"ASCII","is_utf8":true} 
[2025-08-10 23:35:58] local.INFO: WIDDX AI: Processing query {"query":"?????? ???????","language":"ar","session_id":"2227521f-a22a-4011-a001-1ca53335ad38","knowledge_base_enabled":true} 
[2025-08-10 23:35:58] local.INFO: Found similar question match {"question":"?????? ???????","knowledge_id":27,"similarity":0.7500631617732914} 
[2025-08-10 23:35:58] local.INFO: Knowledge base hit {"question":"?????? ???????","knowledge_id":27,"confidence":1.0,"usage_count":3} 
[2025-08-10 23:35:58] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"2227521f-a22a-4011-a001-1ca53335ad38","confidence":1.0,"source":"knowledge_base"} 
[2025-08-10 23:35:58] local.INFO: Chat message processed successfully {"session_id":"2227521f-a22a-4011-a001-1ca53335ad38","user_message_id":51,"ai_message_id":52,"processing_time":167.67} 
[2025-08-10 23:36:54] local.INFO: WIDDX AI: Processing query {"query":"?????? ??? ?????? ??????? ??????","language":"ar","session_id":"205e525e-f85d-48c3-aca5-ba86a8c2740f","knowledge_base_enabled":true} 
[2025-08-10 23:36:54] local.INFO: Found similar question match {"question":"?????? ??? ?????? ??????? ??????","knowledge_id":27,"similarity":0.7857392076601544} 
[2025-08-10 23:36:54] local.INFO: Knowledge base hit {"question":"?????? ??? ?????? ??????? ??????","knowledge_id":27,"confidence":1.0,"usage_count":4} 
[2025-08-10 23:36:54] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"205e525e-f85d-48c3-aca5-ba86a8c2740f","confidence":1.0,"source":"knowledge_base"} 
[2025-08-10 23:36:54] local.INFO: Chat message processed successfully {"session_id":"205e525e-f85d-48c3-aca5-ba86a8c2740f","user_message_id":53,"ai_message_id":54,"processing_time":72.65} 
[2025-08-10 23:39:09] local.INFO: WIDDX AI: Processing query {"query":"مرحبا، هذا اختبار للتشفير العربي الجديد","language":"ar","session_id":"120143e5-0303-4b8d-8525-436c4cd97226","knowledge_base_enabled":true} 
[2025-08-10 23:39:09] local.INFO: Found similar question match {"question":"مرحبا، هذا اختبار للتشفير العربي الجديد","knowledge_id":27,"similarity":1.0} 
[2025-08-10 23:39:10] local.INFO: Knowledge base hit {"question":"مرحبا، هذا اختبار للتشفير العربي الجديد","knowledge_id":27,"confidence":1.0,"usage_count":5} 
[2025-08-10 23:39:10] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"120143e5-0303-4b8d-8525-436c4cd97226","confidence":1.0,"source":"knowledge_base"} 
[2025-08-10 23:39:10] local.INFO: Chat message processed successfully {"session_id":"120143e5-0303-4b8d-8525-436c4cd97226","user_message_id":55,"ai_message_id":56,"processing_time":486.88} 
[2025-08-10 23:43:00] local.INFO: WIDDX AI: Processing query {"query":"Testing new conversation with English message","language":"en","session_id":"cd53bb44-eaa5-42ed-ace4-87708e2f737a","knowledge_base_enabled":true} 
[2025-08-10 23:43:00] local.INFO: Found similar question match {"question":"Testing new conversation with English message","knowledge_id":24,"similarity":0.8299964630497256} 
[2025-08-10 23:43:00] local.INFO: Knowledge base hit {"question":"Testing new conversation with English message","knowledge_id":24,"confidence":1.0,"usage_count":3} 
[2025-08-10 23:43:00] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"cd53bb44-eaa5-42ed-ace4-87708e2f737a","confidence":1.0,"source":"knowledge_base"} 
[2025-08-10 23:43:00] local.INFO: Chat message processed successfully {"session_id":"cd53bb44-eaa5-42ed-ace4-87708e2f737a","user_message_id":57,"ai_message_id":58,"processing_time":61.16} 
[2025-08-10 23:43:28] local.INFO: WIDDX AI: Processing query {"query":"الآن اختبار اللغة العربية في المحادثة الجديدة","language":"ar","session_id":"cd53bb44-eaa5-42ed-ace4-87708e2f737a","knowledge_base_enabled":true} 
[2025-08-10 23:43:28] local.INFO: Found similar question match {"question":"الآن اختبار اللغة العربية في المحادثة الجديدة","knowledge_id":28,"similarity":0.7529235111207591} 
[2025-08-10 23:43:28] local.INFO: Knowledge base hit {"question":"الآن اختبار اللغة العربية في المحادثة الجديدة","knowledge_id":28,"confidence":1.0,"usage_count":3} 
[2025-08-10 23:43:28] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"cd53bb44-eaa5-42ed-ace4-87708e2f737a","confidence":1.0,"source":"knowledge_base"} 
[2025-08-10 23:43:28] local.INFO: Chat message processed successfully {"session_id":"cd53bb44-eaa5-42ed-ace4-87708e2f737a","user_message_id":59,"ai_message_id":60,"processing_time":27.93} 
[2025-08-11 01:21:01] local.INFO: WIDDX AI: Processing query {"query":"مرحبا","language":"ar","session_id":"cd53bb44-eaa5-42ed-ace4-87708e2f737a","knowledge_base_enabled":true} 
[2025-08-11 01:21:01] local.INFO: DeepSeek: Making API request {"query":"مرحبا","language":"ar"} 
[2025-08-11 01:21:09] local.INFO: DeepSeek: API request successful {"results_count":1} 
[2025-08-11 01:21:09] local.INFO: Gemini: Making API request {"query":"مرحبا","language":"ar"} 
[2025-08-11 01:21:11] local.INFO: Gemini: API request successful {"response_length":429} 
[2025-08-11 01:21:12] local.INFO: New knowledge learned {"question":"مرحبا","language":"ar","source_model":"external_api","confidence":1.0,"knowledge_id":30} 
[2025-08-11 01:21:12] local.INFO: WIDDX AI: Query processed successfully {"session_id":"cd53bb44-eaa5-42ed-ace4-87708e2f737a","processing_time":10577.08,"sources_used":1} 
[2025-08-11 01:21:12] local.INFO: Chat message processed successfully {"session_id":"cd53bb44-eaa5-42ed-ace4-87708e2f737a","user_message_id":61,"ai_message_id":62,"processing_time":10577.08} 
[2025-08-11 01:31:38] local.ERROR: Too few arguments to function App\Services\WiddxAiService::__construct(), 3 passed in C:\Users\<USER>\Desktop\New folder (2)\widdx-ai\app\Providers\WiddxServiceProvider.php on line 30 and exactly 4 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Services\\WiddxAiService::__construct(), 3 passed in C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\app\\Providers\\WiddxServiceProvider.php on line 30 and exactly 4 expected at C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\app\\Services\\WiddxAiService.php:17)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\app\\Providers\\WiddxServiceProvider.php(30): App\\Services\\WiddxAiService->__construct(Object(App\\Services\\DeepSeekService), Object(App\\Services\\GeminiService), Object(App\\Services\\LearningService))
#1 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): App\\Providers\\WiddxServiceProvider->App\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\Wi...', Array, true)
#4 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\Wi...', Array)
#5 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Services\\\\Wi...', Array)
#6 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\Wi...')
#7 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#10 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#11 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#13 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(278): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#14 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1104): Illuminate\\Routing\\Route->getController()
#15 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1035): Illuminate\\Routing\\Route->controllerMiddleware()
#16 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(818): Illuminate\\Routing\\Route->gatherMiddleware()
#17 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#18 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#42 {main}
"} 
[2025-08-11 01:31:59] local.ERROR: Too few arguments to function App\Services\WiddxAiService::__construct(), 3 passed in C:\Users\<USER>\Desktop\New folder (2)\widdx-ai\app\Providers\WiddxServiceProvider.php on line 30 and exactly 4 expected {"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Services\\WiddxAiService::__construct(), 3 passed in C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\app\\Providers\\WiddxServiceProvider.php on line 30 and exactly 4 expected at C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\app\\Services\\WiddxAiService.php:17)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\app\\Providers\\WiddxServiceProvider.php(30): App\\Services\\WiddxAiService->__construct(Object(App\\Services\\DeepSeekService), Object(App\\Services\\GeminiService), Object(App\\Services\\LearningService))
#1 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): App\\Providers\\WiddxServiceProvider->App\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Services\\\\Wi...', Array, true)
#4 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Services\\\\Wi...', Array)
#5 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Services\\\\Wi...', Array)
#6 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('App\\\\Services\\\\Wi...')
#7 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#8 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#9 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#10 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#11 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#12 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#13 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(278): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#14 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1104): Illuminate\\Routing\\Route->getController()
#15 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1035): Illuminate\\Routing\\Route->controllerMiddleware()
#16 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(818): Illuminate\\Routing\\Route->gatherMiddleware()
#17 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#18 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#20 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#42 {main}
"} 
[2025-08-11 01:32:40] local.INFO: WIDDX AI: Processing query {"query":"Hello! Can you tell me who you are and what AI model you&#039;re using?","language":"en","session_id":"d054831a-09bd-4a6d-b522-f92277bc7622","knowledge_base_enabled":true} 
[2025-08-11 01:32:40] local.INFO: Found similar question match {"question":"Hello! Can you tell me who you are and what AI model you&#039;re using?","knowledge_id":26,"similarity":0.9059496570817677} 
[2025-08-11 01:32:40] local.INFO: Knowledge base hit {"question":"Hello! Can you tell me who you are and what AI model you&#039;re using?","knowledge_id":26,"confidence":1.0,"usage_count":3} 
[2025-08-11 01:32:40] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"d054831a-09bd-4a6d-b522-f92277bc7622","confidence":1.0,"source":"knowledge_base"} 
[2025-08-11 01:32:40] local.INFO: Chat message processed successfully {"session_id":"d054831a-09bd-4a6d-b522-f92277bc7622","user_message_id":63,"ai_message_id":64,"processing_time":101.5} 
[2025-08-11 01:32:58] local.INFO: WIDDX AI: Processing query {"query":"Are you powered by OpenAI, DeepSeek, Gemini, or Claude? What&#039;s your underlying technology?","language":"en","session_id":"d054831a-09bd-4a6d-b522-f92277bc7622","knowledge_base_enabled":true} 
[2025-08-11 01:32:58] local.INFO: Found similar question match {"question":"Are you powered by OpenAI, DeepSeek, Gemini, or Claude? What&#039;s your underlying technology?","knowledge_id":26,"similarity":0.8888079752829193} 
[2025-08-11 01:32:58] local.INFO: Knowledge base hit {"question":"Are you powered by OpenAI, DeepSeek, Gemini, or Claude? What&#039;s your underlying technology?","knowledge_id":26,"confidence":1.0,"usage_count":4} 
[2025-08-11 01:32:58] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"d054831a-09bd-4a6d-b522-f92277bc7622","confidence":1.0,"source":"knowledge_base"} 
[2025-08-11 01:32:58] local.INFO: Chat message processed successfully {"session_id":"d054831a-09bd-4a6d-b522-f92277bc7622","user_message_id":65,"ai_message_id":66,"processing_time":22.07} 
[2025-08-11 01:33:12] local.INFO: WIDDX AI: Processing query {"query":"من أنت؟ وما هو النموذج الذي تستخدمه؟ هل أنت DeepSeek أم Gemini؟","language":"ar","session_id":"d054831a-09bd-4a6d-b522-f92277bc7622","knowledge_base_enabled":true} 
[2025-08-11 01:33:13] local.INFO: DeepSeek: Making API request {"query":"من أنت؟ وما هو النموذج الذي تستخدمه؟ هل أنت DeepSeek أم Gemini؟","language":"ar"} 
[2025-08-11 01:33:21] local.INFO: WIDDX AI Research: API request successful {"results_count":1} 
[2025-08-11 01:33:21] local.INFO: WIDDX AI: Making API request {"query":"من أنت؟ وما هو النموذج الذي تستخدمه؟ هل أنت DeepSeek أم Gemini؟","language":"ar"} 
[2025-08-11 01:33:31] local.INFO: WIDDX AI: API request successful {"response_length":832} 
[2025-08-11 01:33:31] local.INFO: BrandingService: Filtered AI response {"language":"ar","changes_made":true,"original_length":832,"filtered_length":836} 
[2025-08-11 01:33:31] local.INFO: New knowledge learned {"question":"من أنت؟ وما هو النموذج الذي تستخدمه؟ هل أنت DeepSeek أم Gemini�","language":"ar","source_model":"external_api","confidence":1.0,"knowledge_id":31} 
[2025-08-11 01:33:31] local.INFO: WIDDX AI: Query processed successfully {"session_id":"d054831a-09bd-4a6d-b522-f92277bc7622","processing_time":18319.64,"sources_used":1} 
[2025-08-11 01:33:31] local.INFO: Chat message processed successfully {"session_id":"d054831a-09bd-4a6d-b522-f92277bc7622","user_message_id":67,"ai_message_id":68,"processing_time":18319.64} 
[2025-08-11 01:37:23] local.INFO: WIDDX AI: Processing query {"query":"Hello WIDDX AI! Can you help me understand quantum computing?","language":"en","session_id":"ee63c037-969e-4e3b-85e8-19d91d985e08","knowledge_base_enabled":true} 
[2025-08-11 01:37:23] local.INFO: Found similar question match {"question":"Hello WIDDX AI! Can you help me understand quantum computing?","knowledge_id":21,"similarity":0.7758476980888679} 
[2025-08-11 01:37:23] local.INFO: Knowledge base hit {"question":"Hello WIDDX AI! Can you help me understand quantum computing?","knowledge_id":21,"confidence":1.0,"usage_count":7} 
[2025-08-11 01:37:23] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"ee63c037-969e-4e3b-85e8-19d91d985e08","confidence":1.0,"source":"knowledge_base"} 
[2025-08-11 01:37:24] local.INFO: Chat message processed successfully {"session_id":"ee63c037-969e-4e3b-85e8-19d91d985e08","user_message_id":69,"ai_message_id":70,"processing_time":50.17} 
[2025-08-11 01:42:45] local.INFO: WIDDX AI: Processing query {"query":"This is a test of the new modern interface design!","language":"en","session_id":"8883d8e9-71c9-4c19-bc56-cde5eaca70a3","knowledge_base_enabled":true} 
[2025-08-11 01:42:45] local.INFO: DeepSeek: Making API request {"query":"This is a test of the new modern interface design!","language":"en"} 
[2025-08-11 01:42:51] local.INFO: WIDDX AI Research: API request successful {"results_count":1} 
[2025-08-11 01:42:51] local.INFO: WIDDX AI: Making API request {"query":"This is a test of the new modern interface design!","language":"en"} 
[2025-08-11 01:42:52] local.INFO: WIDDX AI: API request successful {"response_length":177} 
[2025-08-11 01:42:52] local.INFO: BrandingService: Filtered AI response {"language":"en","changes_made":true,"original_length":177,"filtered_length":177} 
[2025-08-11 01:42:52] local.INFO: New knowledge learned {"question":"This is a test of the new modern interface design!","language":"en","source_model":"external_api","confidence":1.0,"knowledge_id":32} 
[2025-08-11 01:42:52] local.INFO: WIDDX AI: Query processed successfully {"session_id":"8883d8e9-71c9-4c19-bc56-cde5eaca70a3","processing_time":7829.09,"sources_used":1} 
[2025-08-11 01:42:52] local.INFO: Chat message processed successfully {"session_id":"8883d8e9-71c9-4c19-bc56-cde5eaca70a3","user_message_id":71,"ai_message_id":72,"processing_time":7829.09} 
[2025-08-11 02:06:25] local.INFO: WIDDX AI: Processing query {"query":"Testing the new premium WIDDX AI interface! This looks amazing!","language":"en","session_id":"52fdf269-4d70-4719-98fc-ab5e4ea4e959","knowledge_base_enabled":true} 
[2025-08-11 02:06:25] local.INFO: Found similar question match {"question":"Testing the new premium WIDDX AI interface! This looks amazing!","knowledge_id":22,"similarity":0.9230386137539709} 
[2025-08-11 02:06:25] local.INFO: Knowledge base hit {"question":"Testing the new premium WIDDX AI interface! This looks amazing!","knowledge_id":22,"confidence":1.0,"usage_count":3} 
[2025-08-11 02:06:25] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"52fdf269-4d70-4719-98fc-ab5e4ea4e959","confidence":1.0,"source":"knowledge_base"} 
[2025-08-11 02:06:25] local.INFO: Chat message processed successfully {"session_id":"52fdf269-4d70-4719-98fc-ab5e4ea4e959","user_message_id":73,"ai_message_id":74,"processing_time":49.11} 
[2025-08-11 02:25:09] local.INFO: WIDDX AI: Processing query {"query":"Wow! This new elite WIDDX AI interface looks absolutely stunning! The design is world-class!","language":"en","session_id":"94db3782-ccbf-491c-8840-615806e774aa","knowledge_base_enabled":true} 
[2025-08-11 02:25:09] local.INFO: Found similar question match {"question":"Wow! This new elite WIDDX AI interface looks absolutely stunning! The design is world-class!","knowledge_id":22,"similarity":0.9524099538770304} 
[2025-08-11 02:25:09] local.INFO: Knowledge base hit {"question":"Wow! This new elite WIDDX AI interface looks absolutely stunning! The design is world-class!","knowledge_id":22,"confidence":1.0,"usage_count":4} 
[2025-08-11 02:25:09] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"94db3782-ccbf-491c-8840-615806e774aa","confidence":1.0,"source":"knowledge_base"} 
[2025-08-11 02:25:09] local.INFO: Chat message processed successfully {"session_id":"94db3782-ccbf-491c-8840-615806e774aa","user_message_id":75,"ai_message_id":76,"processing_time":65.84} 
[2025-08-11 02:41:57] local.INFO: WIDDX AI: Processing query {"query":"مرحبا","language":"ar","session_id":"05f3b18d-f4fe-4c87-b9c2-c5fd4799d8e5","knowledge_base_enabled":true} 
[2025-08-11 02:41:57] local.INFO: Found exact question match {"question":"مرحبا","knowledge_id":30} 
[2025-08-11 02:41:57] local.INFO: Knowledge base hit {"question":"مرحبا","knowledge_id":30,"confidence":1.0,"usage_count":3} 
[2025-08-11 02:41:57] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"05f3b18d-f4fe-4c87-b9c2-c5fd4799d8e5","confidence":1.0,"source":"knowledge_base"} 
[2025-08-11 02:41:57] local.INFO: Chat message processed successfully {"session_id":"05f3b18d-f4fe-4c87-b9c2-c5fd4799d8e5","user_message_id":77,"ai_message_id":78,"processing_time":49.52} 
[2025-08-11 08:08:19] local.INFO: WIDDX AI: Processing query {"query":"Testing the typing indicator fix - it should appear briefly then disappear!","language":"en","session_id":"78f3eda7-4aba-494e-8f91-2f3554f9f71a","knowledge_base_enabled":true} 
[2025-08-11 08:08:19] local.INFO: Found similar question match {"question":"Testing the typing indicator fix - it should appear briefly then disappear!","knowledge_id":26,"similarity":0.8621990367925683} 
[2025-08-11 08:08:19] local.INFO: Knowledge base hit {"question":"Testing the typing indicator fix - it should appear briefly then disappear!","knowledge_id":26,"confidence":1.0,"usage_count":5} 
[2025-08-11 08:08:19] local.INFO: WIDDX AI: Answer found in knowledge base {"session_id":"78f3eda7-4aba-494e-8f91-2f3554f9f71a","confidence":1.0,"source":"knowledge_base"} 
[2025-08-11 08:08:20] local.INFO: Chat message processed successfully {"session_id":"78f3eda7-4aba-494e-8f91-2f3554f9f71a","user_message_id":79,"ai_message_id":80,"processing_time":105.36} 
[2025-08-11 08:54:10] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-11 09:05:39] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-11 09:06:23] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-11 09:21:50] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-11 09:21:55] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-11 09:22:14] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-11 09:29:15] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-11 09:29:26] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-08-11 09:29:31] local.ERROR: The "--columns" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--columns\" option does not exist. at C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('columns', 'uri,name,action...')
#1 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--columns=uri,n...')
#2 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--columns=uri,n...', true)
#3 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Desktop\\New folder (2)\\widdx-ai\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
