<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class DeepSeekService
{
    private ?string $apiKey;
    private string $apiUrl;
    private string $model;
    private float $temperature;
    private int $maxTokens;
    private int $timeout;

    public function __construct()
    {
        $this->apiKey = config('services.deepseek.api_key');
        $this->apiUrl = rtrim(config('services.deepseek.api_url', 'https://api.deepseek.com'), '/');
        $this->model = config('services.deepseek.model', 'deepseek-chat');
        $this->timeout = config('ai.response_timeout', 30);
        $this->temperature = (float) config('services.deepseek.temperature', 0.7);
        $this->maxTokens = (int) config('services.deepseek.max_tokens', 256);
    }

    /**
     * Search for information using DeepSeek API
     *
     * @param string $query
     * @param string $language
     * @return array
     * @throws Exception
     */
    public function search(string $query, string $language = 'en'): array
    {
        // Check if API key is available
        if (empty($this->apiKey)) {
            Log::warning('DeepSeek: API key not configured');
            return [
                'success' => false,
                'error' => 'DeepSeek API key not configured',
                'results' => []
            ];
        }

        try {
            // Create cache key for the query
            $cacheKey = 'deepseek_search_' . md5($query . $language);

            // Check if response is cached
            if (config('ai.cache_responses', true)) {
                $cached = Cache::get($cacheKey);
                if ($cached) {
                    Log::info('DeepSeek: Returning cached response', ['query' => $query]);
                    return $cached;
                }
            }

            Log::info('DeepSeek: Making API request', ['query' => $query, 'language' => $language]);

            $endpoint = sprintf('%s/chat/completions', $this->apiUrl);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])
            ->timeout($this->timeout)
            ->post($endpoint, [
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => $this->buildSystemPrompt($language)],
                    ['role' => 'user', 'content' => $query]
                ],
                'temperature' => $this->temperature,
                'max_tokens' => $this->maxTokens,
                'stream' => false
            ]);

            if (!$response->successful()) {
                throw new Exception('DeepSeek API request failed: ' . $response->body());
            }

            $data = $response->json();

            // Process and structure the response
            $processedData = $this->processSearchResults($data);

            // Cache the response
            if (config('ai.cache_responses', true)) {
                Cache::put($cacheKey, $processedData, config('ai.cache_ttl', 3600));
            }

            Log::info('WIDDX AI Research: API request successful', ['results_count' => count($processedData['results'])]);

            return $processedData;

        } catch (Exception $e) {
            Log::error('WIDDX AI Research API Error: ' . $e->getMessage(), [
                'query' => $query,
                'language' => $language
            ]);

            // Return fallback response
            return $this->getFallbackResponse($query);
        }
    }

    private function buildSystemPrompt(string $language): string
    {
        if ($language === 'ar') {
            return 'أنت WIDDX AI، مساعد ذكي متقدم. اجب باللغة العربية دائماً. قدم إجابات دقيقة ومفيدة ومنظمة. لا تذكر أي أسماء لنماذج أو خدمات ذكاء اصطناعي أخرى. قدم نفسك دائماً باسم WIDDX AI فقط.';
        }
        return 'You are WIDDX AI, an advanced intelligent assistant. Always respond in English. Provide accurate, helpful, and well-structured answers. Never mention any other AI model or service names. Always identify yourself exclusively as WIDDX AI.';
    }

    /**
     * Process and structure search results
     *
     * @param array $data
     * @return array
     */
    private function processSearchResults(array $data): array
    {
        // DeepSeek Chat Completions returns OpenAI-like schema
        $text = '';
        if (isset($data['choices'][0]['message']['content'])) {
            $text = $data['choices'][0]['message']['content'];
        }

        return [
            'query' => '',
            'total_results' => $text ? 1 : 0,
            'results' => $text ? [[
                'title' => 'DeepSeek Chat Completion',
                'content' => $text,
                'source' => 'deepseek-chat',
                'url' => '',
                'relevance_score' => 1,
                'timestamp' => now()->toISOString()
            ]] : [],
            'search_time' => 0,
            'timestamp' => now()->toISOString()
        ];
    }

    /**
     * Get fallback response when API fails
     *
     * @param string $query
     * @return array
     */
    private function getFallbackResponse(string $query): array
    {
        return [
            'query' => $query,
            'total_results' => 0,
            'results' => [],
            'search_time' => 0,
            'timestamp' => now()->toISOString(),
            'fallback' => true,
            'message' => 'WIDDX AI research service temporarily unavailable. Please try again later.'
        ];
    }

    /**
     * Check if DeepSeek service is available
     *
     * @return bool
     */
    public function isAvailable(): bool
    {
        try {
            // Try a lightweight models list to confirm availability
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ])
            ->timeout(5)
            ->get($this->apiUrl . '/models');

            return $response->successful();
        } catch (Exception $e) {
            Log::warning('DeepSeek health check failed: ' . $e->getMessage());
            return false;
        }
    }
}
