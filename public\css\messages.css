/* WIDDX AI - Elite Message System */
/* Advanced Message Components */

/* Elite Message Cards */
.message-card {
    margin-bottom: 32px;
    display: flex;
    flex-direction: column;
    position: relative;
    animation: messageSlideIn 0.4s var(--transition-bounce);
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-card.user {
    align-items: flex-end;
}

.message-card.ai {
    align-items: flex-start;
}

.message-body {
    max-width: 85%;
    padding: 20px 24px;
    border-radius: var(--border-radius-2xl);
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    word-wrap: break-word;
    position: relative;
    transition: all var(--transition-normal);
}

.message-card.user .message-body {
    background: var(--user-bg);
    color: var(--user-text);
    box-shadow: var(--user-shadow);
    border-bottom-right-radius: var(--border-radius-sm);
}

.message-card.user .message-body:hover {
    background: var(--user-bg-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.message-card.ai .message-body {
    background: var(--ai-bg);
    color: var(--ai-text);
    border: 1px solid var(--ai-border);
    box-shadow: var(--ai-shadow);
    border-bottom-left-radius: var(--border-radius-sm);
}

.message-card.ai .message-body:hover {
    background: var(--ai-bg-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.message-time {
    font-size: var(--font-size-xs);
    color: var(--text-quaternary);
    margin-top: 8px;
    font-weight: var(--font-weight-normal);
    padding: 0 6px;
    opacity: 0.8;
}

/* Elite Message Input */
.message-input {
    flex: 1;
    border: 2px solid var(--bg-tertiary);
    border-radius: var(--border-radius-2xl);
    padding: 18px 24px;
    font-size: var(--font-size-base);
    outline: none;
    transition: all var(--transition-normal);
    background: var(--bg-input);
    color: var(--text-primary);
    resize: none;
    min-height: 56px;
    max-height: 140px;
    font-family: var(--font-family);
    line-height: var(--line-height-normal);
    box-shadow: var(--shadow-sm);
    position: relative;
}

.message-input::placeholder {
    color: var(--text-quaternary);
    font-weight: var(--font-weight-normal);
}

.message-input:focus {
    border-color: var(--brand-primary);
    box-shadow: var(--focus-ring), var(--shadow-md);
    background: var(--bg-primary);
    transform: translateY(-2px);
}

.message-input:hover:not(:focus) {
    border-color: var(--hover-border);
    box-shadow: var(--shadow-md);
}

/* Elite Send Button */
.send-btn {
    width: 56px;
    height: 56px;
    border-radius: var(--border-radius-2xl);
    background: var(--brand-gradient);
    border: none;
    color: var(--text-inverse);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
    cursor: pointer;
    font-size: var(--font-size-lg);
    box-shadow: var(--shadow-md);
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.send-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-normal);
}

.send-btn:hover:not(:disabled) {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-xl);
    background: var(--brand-gradient-reverse);
}

.send-btn:hover:not(:disabled)::before {
    left: 100%;
}

.send-btn:active:not(:disabled) {
    transform: translateY(-1px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.send-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
    box-shadow: var(--shadow-sm);
}

.send-btn i {
    transition: transform var(--transition-normal);
}

.send-btn:hover:not(:disabled) i {
    transform: translateX(2px);
}

/* Elite Typing Indicator */
.typing-indicator {
    display: none;
    padding: 20px;
    color: var(--text-tertiary);
    background: var(--bg-primary);
    border-top: 1px solid var(--bg-tertiary);
    font-size: var(--font-size-sm);
    justify-content: center;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.typing-indicator.show {
    display: flex;
}

.typing-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-glass);
    z-index: -1;
}

.typing-content {
    max-width: var(--max-width);
    width: 100%;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0 var(--content-padding);
    position: relative;
    z-index: 1;
}

.typing-dots {
    display: flex;
    align-items: center;
    gap: 4px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--brand-primary);
    animation: typingElite 1.6s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

@keyframes typingElite {
    0%, 80%, 100% { 
        transform: scale(0.5) translateY(0); 
        opacity: 0.4; 
    }
    40% { 
        transform: scale(1.2) translateY(-8px); 
        opacity: 1; 
    }
}
