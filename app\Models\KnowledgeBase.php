<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class KnowledgeBase extends Model
{
    use HasFactory;

    protected $fillable = [
        'question',
        'answer',
        'question_embedding',
        'language',
        'confidence_score',
        'usage_count',
        'quality_rating',
        'source_model',
        'metadata',
        'question_hash',
        'last_used_at'
    ];

    protected $casts = [
        'question_embedding' => 'array',
        'confidence_score' => 'float',
        'usage_count' => 'integer',
        'quality_rating' => 'float',
        'metadata' => 'array',
        'last_used_at' => 'datetime'
    ];

    /**
     * Source models
     */
    const MODEL_DEEPSEEK = 'deepseek';
    const MODEL_GEMINI = 'gemini';
    const MODEL_HYBRID = 'hybrid';

    /**
     * Generate hash for question normalization
     */
    public static function generateQuestionHash(string $question, string $language = 'en'): string
    {
        // Normalize question: lowercase, trim, remove extra spaces
        $normalized = strtolower(trim(preg_replace('/\s+/', ' ', $question)));
        return hash('sha256', $language . ':' . $normalized);
    }

    /**
     * Increment usage count and update last used timestamp
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => Carbon::now()]);
    }

    /**
     * Update quality rating
     */
    public function updateQualityRating(float $rating): void
    {
        $this->update(['quality_rating' => max(1, min(5, $rating))]);
    }

    /**
     * Scope for finding similar questions by hash
     */
    public function scopeByQuestionHash($query, string $hash)
    {
        return $query->where('question_hash', $hash);
    }

    /**
     * Scope for filtering by language
     */
    public function scopeByLanguage($query, string $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Scope for filtering by minimum confidence
     */
    public function scopeMinConfidence($query, float $minConfidence)
    {
        return $query->where('confidence_score', '>=', $minConfidence);
    }

    /**
     * Scope for ordering by relevance (usage + quality + confidence)
     */
    public function scopeOrderByRelevance($query)
    {
        return $query->orderByRaw('(usage_count * 0.3 + COALESCE(quality_rating, 3) * 0.4 + confidence_score * 0.3) DESC');
    }
}
