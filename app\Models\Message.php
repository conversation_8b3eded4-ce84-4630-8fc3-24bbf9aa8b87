<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Message extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'conversation_id',
        'type',
        'content',
        'language',
        'processing_time',
        'sources_used',
        'quality_score',
        'model_info',
        'metadata',
        'error_info'
    ];

    protected $casts = [
        'processing_time' => 'float',
        'sources_used' => 'integer',
        'quality_score' => 'float',
        'model_info' => 'array',
        'metadata' => 'array',
        'error_info' => 'array'
    ];

    /**
     * Message types
     */
    const TYPE_USER = 'user';
    const TYPE_AI = 'ai';
    const TYPE_SYSTEM = 'system';

    /**
     * Get the conversation that owns this message
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(Conversation::class);
    }

    /**
     * Scope to get user messages
     */
    public function scopeUserMessages($query)
    {
        return $query->where('type', self::TYPE_USER);
    }

    /**
     * Scope to get AI messages
     */
    public function scopeAiMessages($query)
    {
        return $query->where('type', self::TYPE_AI);
    }

    /**
     * Scope to get messages by language
     */
    public function scopeByLanguage($query, string $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Scope to get messages with errors
     */
    public function scopeWithErrors($query)
    {
        return $query->whereNotNull('error_info');
    }

    /**
     * Scope to get high quality messages
     */
    public function scopeHighQuality($query, float $threshold = 0.7)
    {
        return $query->where('quality_score', '>=', $threshold);
    }

    /**
     * Check if this is a user message
     */
    public function isUserMessage(): bool
    {
        return $this->type === self::TYPE_USER;
    }

    /**
     * Check if this is an AI message
     */
    public function isAiMessage(): bool
    {
        return $this->type === self::TYPE_AI;
    }

    /**
     * Check if this message has errors
     */
    public function hasErrors(): bool
    {
        return !empty($this->error_info);
    }

    /**
     * Get the sources used in this message
     */
    public function getSources(): array
    {
        return $this->metadata['sources'] ?? [];
    }

    /**
     * Get performance metrics for this message
     */
    public function getPerformanceMetrics(): array
    {
        return [
            'processing_time' => $this->processing_time,
            'sources_used' => $this->sources_used,
            'quality_score' => $this->quality_score,
            'model_used' => $this->model_info['ai_model'] ?? 'unknown',
            'search_engine' => $this->model_info['search_engine'] ?? 'unknown',
            'fallback_used' => $this->metadata['fallback_used'] ?? false
        ];
    }

    /**
     * Create a user message
     */
    public static function createUserMessage(
        int $conversationId,
        string $content,
        string $language = 'en'
    ): self {
        return self::create([
            'conversation_id' => $conversationId,
            'type' => self::TYPE_USER,
            'content' => $content,
            'language' => $language,
            'metadata' => [
                'timestamp' => now()->toISOString()
            ]
        ]);
    }

    /**
     * Create an AI message from WIDDX AI response
     */
    public static function createAiMessage(
        int $conversationId,
        array $aiResponse
    ): self {
        return self::create([
            'conversation_id' => $conversationId,
            'type' => self::TYPE_AI,
            'content' => $aiResponse['response'],
            'language' => $aiResponse['language'],
            'processing_time' => $aiResponse['processing_time'],
            'sources_used' => $aiResponse['sources_used'],
            'quality_score' => $aiResponse['metadata']['quality_score'] ?? 0,
            'model_info' => $aiResponse['model_info'] ?? [],
            'metadata' => $aiResponse['metadata'] ?? [],
            'error_info' => isset($aiResponse['error']) ? $aiResponse['error'] : null
        ]);
    }

    /**
     * Get formatted content for display
     */
    public function getFormattedContent(): string
    {
        // Basic markdown-like formatting
        $content = $this->content;
        
        // Convert line breaks
        $content = nl2br($content);
        
        // Basic formatting (this could be expanded)
        $content = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $content);
        $content = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $content);
        
        return $content;
    }
}