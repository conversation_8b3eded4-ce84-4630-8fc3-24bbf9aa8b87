<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('knowledge_bases', function (Blueprint $table) {
            $table->id();
            $table->text('question'); // Original user question
            $table->longText('answer'); // AI-generated answer
            $table->json('question_embedding')->nullable(); // Vector embedding of the question
            $table->string('language', 10)->default('en'); // Language of the Q&A
            $table->float('confidence_score')->default(0.0); // Confidence in the answer
            $table->integer('usage_count')->default(1); // How many times this was used
            $table->float('quality_rating')->nullable(); // User feedback rating (1-5)
            $table->string('source_model', 50)->nullable(); // Which AI model generated this (deepseek/gemini)
            $table->json('metadata')->nullable(); // Additional metadata
            $table->string('question_hash', 64)->index(); // Hash of normalized question for quick lookup
            $table->timestamp('last_used_at')->nullable(); // When this was last retrieved
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['language', 'confidence_score']);
            $table->index(['usage_count', 'quality_rating']);
            $table->index('last_used_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('knowledge_bases');
    }
};
