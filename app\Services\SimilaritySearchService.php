<?php

namespace App\Services;

use App\Models\KnowledgeBase;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

class SimilaritySearchService
{
    private EmbeddingService $embeddingService;
    private float $similarityThreshold;
    private int $maxResults;

    public function __construct(EmbeddingService $embeddingService)
    {
        $this->embeddingService = $embeddingService;
        $this->similarityThreshold = config('widdx.similarity.threshold', 0.75);
        $this->maxResults = config('widdx.similarity.max_results', 5);
    }

    /**
     * Search for similar questions in the knowledge base
     */
    public function searchSimilarQuestions(string $question, string $language = 'en', float $minConfidence = 0.5): ?KnowledgeBase
    {
        try {
            // First, try exact hash match for identical questions
            $questionHash = KnowledgeBase::generateQuestionHash($question, $language);
            $exactMatch = KnowledgeBase::byQuestionHash($questionHash)
                ->byLanguage($language)
                ->minConfidence($minConfidence)
                ->orderByRelevance()
                ->first();

            if ($exactMatch) {
                Log::info('Found exact question match', [
                    'question' => substr($question, 0, 100),
                    'knowledge_id' => $exactMatch->id
                ]);
                return $exactMatch;
            }

            // Generate embedding for the question
            $questionEmbedding = $this->embeddingService->generateEmbedding($question, $language);
            if (!$questionEmbedding) {
                Log::warning('Failed to generate embedding for question', [
                    'question' => substr($question, 0, 100)
                ]);
                return null;
            }

            // Get candidate knowledge base entries
            $candidates = $this->getCandidateEntries($language, $minConfidence);
            if ($candidates->isEmpty()) {
                return null;
            }

            // Calculate similarities
            $similarities = $this->calculateSimilarities($questionEmbedding, $candidates);
            
            // Find the best match above threshold
            $bestMatch = $this->findBestMatch($similarities);
            
            if ($bestMatch) {
                Log::info('Found similar question match', [
                    'question' => substr($question, 0, 100),
                    'knowledge_id' => $bestMatch['entry']->id,
                    'similarity' => $bestMatch['similarity']
                ]);
                return $bestMatch['entry'];
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Similarity search failed', [
                'question' => substr($question, 0, 100),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Search for multiple similar questions
     */
    public function searchMultipleSimilarQuestions(string $question, string $language = 'en', float $minConfidence = 0.5): Collection
    {
        try {
            // Generate embedding for the question
            $questionEmbedding = $this->embeddingService->generateEmbedding($question, $language);
            if (!$questionEmbedding) {
                return collect();
            }

            // Get candidate knowledge base entries
            $candidates = $this->getCandidateEntries($language, $minConfidence);
            if ($candidates->isEmpty()) {
                return collect();
            }

            // Calculate similarities
            $similarities = $this->calculateSimilarities($questionEmbedding, $candidates);
            
            // Return all matches above threshold, sorted by similarity
            return collect($similarities)
                ->filter(fn($item) => $item['similarity'] >= $this->similarityThreshold)
                ->sortByDesc('similarity')
                ->take($this->maxResults)
                ->map(fn($item) => [
                    'entry' => $item['entry'],
                    'similarity' => $item['similarity']
                ]);
        } catch (\Exception $e) {
            Log::error('Multiple similarity search failed', [
                'question' => substr($question, 0, 100),
                'error' => $e->getMessage()
            ]);
            return collect();
        }
    }

    /**
     * Get candidate entries from knowledge base
     */
    private function getCandidateEntries(string $language, float $minConfidence): Collection
    {
        return KnowledgeBase::byLanguage($language)
            ->minConfidence($minConfidence)
            ->whereNotNull('question_embedding')
            ->orderByRelevance()
            ->limit(1000) // Limit to prevent memory issues
            ->get();
    }

    /**
     * Calculate similarities between question and candidates
     */
    private function calculateSimilarities(array $questionEmbedding, Collection $candidates): array
    {
        $similarities = [];

        foreach ($candidates as $candidate) {
            if (!$candidate->question_embedding) {
                continue;
            }

            try {
                $similarity = $this->embeddingService->calculateSimilarity(
                    $questionEmbedding,
                    $candidate->question_embedding
                );

                $similarities[] = [
                    'entry' => $candidate,
                    'similarity' => $similarity
                ];
            } catch (\Exception $e) {
                Log::warning('Failed to calculate similarity', [
                    'candidate_id' => $candidate->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $similarities;
    }

    /**
     * Find the best match above threshold
     */
    private function findBestMatch(array $similarities): ?array
    {
        $bestMatch = null;
        $bestSimilarity = 0;

        foreach ($similarities as $item) {
            if ($item['similarity'] >= $this->similarityThreshold && $item['similarity'] > $bestSimilarity) {
                $bestMatch = $item;
                $bestSimilarity = $item['similarity'];
            }
        }

        return $bestMatch;
    }

    /**
     * Update similarity threshold
     */
    public function setSimilarityThreshold(float $threshold): void
    {
        $this->similarityThreshold = max(0.0, min(1.0, $threshold));
    }

    /**
     * Get current similarity threshold
     */
    public function getSimilarityThreshold(): float
    {
        return $this->similarityThreshold;
    }

    /**
     * Set maximum results
     */
    public function setMaxResults(int $maxResults): void
    {
        $this->maxResults = max(1, $maxResults);
    }

    /**
     * Get statistics about knowledge base coverage
     */
    public function getKnowledgeBaseStats(string $language = null): array
    {
        $query = KnowledgeBase::query();
        
        if ($language) {
            $query->byLanguage($language);
        }

        $total = $query->count();
        $withEmbeddings = $query->whereNotNull('question_embedding')->count();
        $highConfidence = $query->minConfidence(0.8)->count();
        $recentlyUsed = $query->where('last_used_at', '>=', now()->subDays(7))->count();
        $avgQuality = $query->whereNotNull('quality_rating')->avg('quality_rating');

        return [
            'total_entries' => $total,
            'entries_with_embeddings' => $withEmbeddings,
            'high_confidence_entries' => $highConfidence,
            'recently_used_entries' => $recentlyUsed,
            'average_quality_rating' => round($avgQuality, 2),
            'embedding_coverage' => $total > 0 ? round(($withEmbeddings / $total) * 100, 2) : 0
        ];
    }

    /**
     * Rebuild embeddings for entries missing them
     */
    public function rebuildMissingEmbeddings(int $batchSize = 50): int
    {
        $entriesWithoutEmbeddings = KnowledgeBase::whereNull('question_embedding')
            ->limit($batchSize)
            ->get();

        $processed = 0;

        foreach ($entriesWithoutEmbeddings as $entry) {
            try {
                $embedding = $this->embeddingService->generateEmbedding($entry->question, $entry->language);
                if ($embedding) {
                    $entry->update(['question_embedding' => $embedding]);
                    $processed++;
                }
            } catch (\Exception $e) {
                Log::error('Failed to rebuild embedding', [
                    'entry_id' => $entry->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $processed;
    }
}