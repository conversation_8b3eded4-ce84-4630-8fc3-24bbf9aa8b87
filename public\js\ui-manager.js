/**
 * WIDDX AI - Elite UI Manager
 * Advanced UI State Management and Interactions
 */

class WiddxUIManager {
    constructor() {
        this.isMobile = false;
        this.isTablet = false;
        this.sidebarVisible = false;
        this.lastScrollPosition = 0;
        
        this.initializeElements();
        this.detectDeviceType();
        this.bindEvents();
        this.initializeUI();
    }

    initializeElements() {
        this.sidebar = document.getElementById('sidebar');
        this.sidebarOverlay = document.getElementById('sidebarOverlay');
        this.mobileToggle = document.querySelector('.mobile-toggle');
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.newChatBtn = document.querySelector('.new-chat-btn');
    }

    detectDeviceType() {
        this.isMobile = window.innerWidth <= 768;
        this.isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
        
        // Update CSS custom properties based on device
        this.updateDeviceSpecificStyles();
    }

    updateDeviceSpecificStyles() {
        const root = document.documentElement;
        
        if (this.isMobile) {
            root.style.setProperty('--sidebar-width', '300px');
            root.style.setProperty('--content-padding', '16px');
            root.style.setProperty('--header-height', '56px');
        } else if (this.isTablet) {
            root.style.setProperty('--sidebar-width', '260px');
            root.style.setProperty('--content-padding', '20px');
            root.style.setProperty('--header-height', '60px');
        } else {
            root.style.setProperty('--sidebar-width', '280px');
            root.style.setProperty('--content-padding', '24px');
            root.style.setProperty('--header-height', '64px');
        }
    }

    bindEvents() {
        // Window resize event
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));

        // Mobile toggle event
        if (this.mobileToggle) {
            this.mobileToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // Sidebar overlay event
        if (this.sidebarOverlay) {
            this.sidebarOverlay.addEventListener('click', () => {
                this.hideSidebar();
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Touch events for mobile
        if (this.isMobile) {
            this.bindTouchEvents();
        }

        // Scroll events
        if (this.chatMessages) {
            this.chatMessages.addEventListener('scroll', this.throttle(() => {
                this.handleScroll();
            }, 100));
        }

        // Focus management
        this.bindFocusEvents();

        // New chat button
        if (this.newChatBtn) {
            this.newChatBtn.addEventListener('click', () => {
                this.startNewConversation();
            });
        }
    }

    initializeUI() {
        // Set initial sidebar state
        if (this.isMobile) {
            this.hideSidebar();
        } else {
            this.showSidebar();
        }

        // Initialize accessibility features
        this.initializeAccessibility();

        // Add loading states
        this.initializeLoadingStates();

        // Initialize animations
        this.initializeAnimations();
    }

    handleResize() {
        const wasMobile = this.isMobile;
        this.detectDeviceType();

        // Handle mobile/desktop transition
        if (wasMobile !== this.isMobile) {
            if (this.isMobile) {
                this.hideSidebar();
            } else {
                this.showSidebar();
                this.hideSidebarOverlay();
            }
        }

        // Update textarea height
        if (this.messageInput) {
            this.autoResizeTextarea();
        }
    }

    toggleSidebar() {
        if (this.sidebarVisible) {
            this.hideSidebar();
        } else {
            this.showSidebar();
        }
    }

    showSidebar() {
        if (!this.sidebar) return;

        this.sidebar.classList.add('show');
        this.sidebarVisible = true;

        if (this.isMobile) {
            this.showSidebarOverlay();
            document.body.style.overflow = 'hidden';
        }

        // Dispatch event
        this.dispatchUIEvent('sidebarShow');
    }

    hideSidebar() {
        if (!this.sidebar) return;

        this.sidebar.classList.remove('show');
        this.sidebarVisible = false;

        if (this.isMobile) {
            this.hideSidebarOverlay();
            document.body.style.overflow = '';
        }

        // Dispatch event
        this.dispatchUIEvent('sidebarHide');
    }

    showSidebarOverlay() {
        if (this.sidebarOverlay) {
            this.sidebarOverlay.classList.add('show');
        }
    }

    hideSidebarOverlay() {
        if (this.sidebarOverlay) {
            this.sidebarOverlay.classList.remove('show');
        }
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + / to toggle sidebar
        if ((e.ctrlKey || e.metaKey) && e.key === '/') {
            e.preventDefault();
            this.toggleSidebar();
        }

        // Escape to close sidebar on mobile
        if (e.key === 'Escape' && this.isMobile && this.sidebarVisible) {
            this.hideSidebar();
        }

        // Ctrl/Cmd + N for new conversation
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            this.startNewConversation();
        }

        // Focus message input with Ctrl/Cmd + K
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            if (this.messageInput) {
                this.messageInput.focus();
            }
        }
    }

    bindTouchEvents() {
        let touchStartX = 0;
        let touchStartY = 0;

        document.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', (e) => {
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;

            // Swipe right to open sidebar (from left edge)
            if (deltaX > 50 && Math.abs(deltaY) < 100 && touchStartX < 50) {
                this.showSidebar();
            }

            // Swipe left to close sidebar
            if (deltaX < -50 && Math.abs(deltaY) < 100 && this.sidebarVisible) {
                this.hideSidebar();
            }
        });
    }

    handleScroll() {
        const currentScrollPosition = this.chatMessages.scrollTop;
        const scrollDirection = currentScrollPosition > this.lastScrollPosition ? 'down' : 'up';
        
        this.lastScrollPosition = currentScrollPosition;

        // Dispatch scroll event
        this.dispatchUIEvent('scroll', { direction: scrollDirection, position: currentScrollPosition });
    }

    bindFocusEvents() {
        // Focus trap for sidebar on mobile
        if (this.isMobile && this.sidebar) {
            this.sidebar.addEventListener('keydown', (e) => {
                if (e.key === 'Tab') {
                    this.handleFocusTrap(e);
                }
            });
        }

        // Auto-focus message input when appropriate
        document.addEventListener('click', (e) => {
            if (e.target.closest('.chat-messages') && this.messageInput) {
                this.messageInput.focus();
            }
        });
    }

    handleFocusTrap(e) {
        const focusableElements = this.sidebar.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }

    initializeAccessibility() {
        // Add ARIA labels
        if (this.sidebar) {
            this.sidebar.setAttribute('aria-label', 'Conversation history');
        }

        if (this.mobileToggle) {
            this.mobileToggle.setAttribute('aria-label', 'Toggle sidebar');
        }

        // Add focus indicators
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
    }

    initializeLoadingStates() {
        // Add loading class utilities
        this.addLoadingState = (element) => {
            if (element) {
                element.classList.add('loading');
            }
        };

        this.removeLoadingState = (element) => {
            if (element) {
                element.classList.remove('loading');
            }
        };
    }

    initializeAnimations() {
        // Reduce motion if user prefers
        if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.body.classList.add('reduce-motion');
        }

        // Add entrance animations
        this.addEntranceAnimations();
    }

    addEntranceAnimations() {
        const animatedElements = document.querySelectorAll('.fade-in, .slide-up, .scale-in');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                }
            });
        });

        animatedElements.forEach(el => observer.observe(el));
    }

    autoResizeTextarea() {
        if (!this.messageInput) return;
        
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 140) + 'px';
    }

    startNewConversation() {
        if (window.chatApp) {
            window.chatApp.currentConversationId = null;
            window.chatApp.chatTitle.textContent = 'New Conversation';
            window.chatApp.messagesContainer.innerHTML = '';
            window.chatApp.emptyState.style.display = 'flex';
            window.chatApp.renderConversations();
            window.chatApp.messageInput.focus();

            // Close sidebar on mobile
            if (this.isMobile) {
                this.hideSidebar();
            }
        }
    }

    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    dispatchUIEvent(eventName, detail = {}) {
        const event = new CustomEvent(`ui${eventName}`, { detail });
        window.dispatchEvent(event);
    }

    // Public API
    getDeviceType() {
        if (this.isMobile) return 'mobile';
        if (this.isTablet) return 'tablet';
        return 'desktop';
    }

    isSidebarVisible() {
        return this.sidebarVisible;
    }
}

// Global UI management functions
function toggleSidebar() {
    if (window.widdxUIManager) {
        window.widdxUIManager.toggleSidebar();
    }
}

function startNewConversation() {
    if (window.widdxUIManager) {
        window.widdxUIManager.startNewConversation();
    }
}

// Export for use in other modules
window.WiddxUIManager = WiddxUIManager;
window.toggleSidebar = toggleSidebar;
window.startNewConversation = startNewConversation;
