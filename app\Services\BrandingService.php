<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class BrandingService
{
    /**
     * List of AI model/service names to filter out
     */
    private const FILTERED_MODELS = [
        'DeepSeek', 'deepseek', 'DEEPSEEK',
        '<PERSON>', 'gemini', '<PERSON><PERSON><PERSON>',
        'OpenA<PERSON>', 'openai', 'OPEN<PERSON><PERSON>',
        '<PERSON>', 'claude', '<PERSON><PERSON>U<PERSON>',
        'GPT', 'gpt', 'ChatGPT', 'chatgpt',
        'Anthropic', 'anthropic', 'ANTHROPIC',
        'Google AI', 'google ai', 'GOOGLE AI',
        'Bard', 'bard', 'BA<PERSON>',
        'LLaMA', 'llama', 'LLAMA',
        'Pa<PERSON>', 'palm', 'PALM',
        'Mistral', 'mistral', 'MISTRAL',
        'Cohere', 'cohere', 'COHERE'
    ];

    /**
     * Phrases that might reveal AI model identity
     */
    private const FILTERED_PHRASES = [
        'I am DeepSeek', 'I\'m <PERSON><PERSON>eek', 'I am <PERSON>', 'I\'m <PERSON>',
        'I am <PERSON>', 'I\'m <PERSON>', 'I am GPT', 'I\'m GP<PERSON>',
        'I am ChatGPT', 'I\'m ChatGPT', 'I am OpenAI', 'I\'m OpenAI',
        'As DeepSeek', 'As Gemini', 'As Claude', 'As GPT', 'As ChatGPT',
        'powered by DeepSeek', 'powered by Gemini', 'powered by OpenAI',
        'developed by DeepSeek', 'developed by Google', 'developed by OpenAI',
        'created by DeepSeek', 'created by Google', 'created by OpenAI',
        'made by DeepSeek', 'made by Google', 'made by OpenAI',
        'from DeepSeek', 'from Google', 'from OpenAI', 'from Anthropic',
        
        // Arabic equivalents
        'أنا DeepSeek', 'أنا Gemini', 'أنا Claude', 'أنا GPT',
        'بواسطة DeepSeek', 'بواسطة Gemini', 'بواسطة OpenAI',
        'من تطوير DeepSeek', 'من تطوير Google', 'من تطوير OpenAI',
        'من صنع DeepSeek', 'من صنع Google', 'من صنع OpenAI'
    ];

    /**
     * Filter AI response to enforce WIDDX AI branding
     *
     * @param string $response
     * @param string $language
     * @return string
     */
    public function filterResponse(string $response, string $language = 'en'): string
    {
        $originalResponse = $response;
        
        // Remove model name references
        foreach (self::FILTERED_MODELS as $model) {
            $response = str_ireplace($model, 'WIDDX AI', $response);
        }
        
        // Remove identity-revealing phrases
        foreach (self::FILTERED_PHRASES as $phrase) {
            if ($language === 'ar') {
                $replacement = 'أنا WIDDX AI';
            } else {
                $replacement = 'I am WIDDX AI';
            }
            $response = str_ireplace($phrase, $replacement, $response);
        }
        
        // Ensure WIDDX AI introduction if the response starts with self-identification
        if ($language === 'ar') {
            // Arabic patterns
            if (preg_match('/^(أنا|انا)\s+/u', $response) && !preg_match('/WIDDX AI/u', $response)) {
                $response = preg_replace('/^(أنا|انا)\s+[^،.!؟]*/', 'أنا WIDDX AI', $response);
            }
        } else {
            // English patterns
            if (preg_match('/^(I am|I\'m|Hello, I am|Hi, I am)\s+/i', $response) && !preg_match('/WIDDX AI/i', $response)) {
                $response = preg_replace('/^(I am|I\'m|Hello, I am|Hi, I am)\s+[^,.!?]*/', 'I am WIDDX AI', $response);
            }
        }
        
        // Log filtering if changes were made
        if ($originalResponse !== $response) {
            Log::info('BrandingService: Filtered AI response', [
                'language' => $language,
                'changes_made' => true,
                'original_length' => strlen($originalResponse),
                'filtered_length' => strlen($response)
            ]);
        }
        
        return $response;
    }

    /**
     * Get branded introduction message
     *
     * @param string $language
     * @return string
     */
    public function getBrandedIntroduction(string $language = 'en'): string
    {
        if ($language === 'ar') {
            return 'مرحباً! أنا WIDDX AI، مساعدك الذكي المتقدم. كيف يمكنني مساعدتك اليوم؟';
        }
        
        return 'Hello! I\'m WIDDX AI, your advanced intelligent assistant. How can I help you today?';
    }

    /**
     * Get branded error message
     *
     * @param string $language
     * @return string
     */
    public function getBrandedErrorMessage(string $language = 'en'): string
    {
        if ($language === 'ar') {
            return 'عذراً، أواجه صعوبة في معالجة طلبك حالياً. أنا WIDDX AI وأعمل على حل هذه المشكلة. يرجى المحاولة مرة أخرى.';
        }
        
        return 'I apologize, but I\'m having difficulty processing your request right now. I\'m WIDDX AI and I\'m working to resolve this issue. Please try again.';
    }

    /**
     * Filter model information from metadata
     *
     * @param array $metadata
     * @return array
     */
    public function filterMetadata(array $metadata): array
    {
        // Replace model names in metadata
        if (isset($metadata['model_info']['ai_model'])) {
            $aiModel = $metadata['model_info']['ai_model'];
            foreach (self::FILTERED_MODELS as $model) {
                if (stripos($aiModel, $model) !== false) {
                    $metadata['model_info']['ai_model'] = 'WIDDX AI';
                    break;
                }
            }
        }
        
        // Remove source information that might reveal underlying services
        if (isset($metadata['sources'])) {
            foreach ($metadata['sources'] as &$source) {
                if (isset($source['source'])) {
                    foreach (self::FILTERED_MODELS as $model) {
                        $source['source'] = str_ireplace($model, 'WIDDX AI', $source['source']);
                    }
                }
            }
        }
        
        return $metadata;
    }
}
