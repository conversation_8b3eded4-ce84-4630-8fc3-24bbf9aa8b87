<?php

namespace Tests\Unit;

use App\Services\EmbeddingService;
use Tests\TestCase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class MockEmbeddingServiceTest extends TestCase
{
    private EmbeddingService $embeddingService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test configuration
        config([
            'widdx.embedding.provider' => 'mock',
            'widdx.embedding.cache_ttl' => 3600,
            'widdx.embedding.dimensions' => 384,
        ]);
        
        $this->embeddingService = new class extends EmbeddingService {
            public function generateEmbedding(string $text, string $language = 'en'): ?array
            {
                // Mock embedding generation for testing
                $mockEmbedding = [];
                $dimension = config('widdx.embedding.dimensions', 384);
                
                // Generate a simple mock embedding based on text hash
                $hash = crc32($text);
                for ($i = 0; $i < $dimension; $i++) {
                    $mockEmbedding[] = sin($hash + $i) * 0.1;
                }
                
                // Normalize the vector
                $magnitude = sqrt(array_sum(array_map(fn($x) => $x * $x, $mockEmbedding)));
                if ($magnitude > 0) {
                    $mockEmbedding = array_map(fn($x) => $x / $magnitude, $mockEmbedding);
                }
                
                return $mockEmbedding;
            }
        };
    }

    /** @test */
    public function it_can_generate_mock_embeddings()
    {
        $text = 'This is a test sentence';
        $embedding = $this->embeddingService->generateEmbedding($text);
        
        $this->assertIsArray($embedding);
        $this->assertCount(384, $embedding);
        $this->assertIsFloat($embedding[0]);
        
        // Test that the same text produces the same embedding
        $embedding2 = $this->embeddingService->generateEmbedding($text);
        $this->assertEquals($embedding, $embedding2);
    }

    /** @test */
    public function it_can_calculate_similarity_between_embeddings()
    {
        $text1 = 'Laravel is a PHP framework';
        $text2 = 'Laravel is a web framework';
        $text3 = 'Python is a programming language';
        
        $embedding1 = $this->embeddingService->generateEmbedding($text1);
        $embedding2 = $this->embeddingService->generateEmbedding($text2);
        $embedding3 = $this->embeddingService->generateEmbedding($text3);
        
        $similarity12 = $this->embeddingService->calculateSimilarity($embedding1, $embedding2);
        $similarity13 = $this->embeddingService->calculateSimilarity($embedding1, $embedding3);
        
        $this->assertIsFloat($similarity12);
        $this->assertIsFloat($similarity13);
        $this->assertGreaterThanOrEqual(-1, $similarity12);
        $this->assertLessThanOrEqual(1, $similarity12);
        $this->assertGreaterThanOrEqual(-1, $similarity13);
        $this->assertLessThanOrEqual(1, $similarity13);
    }

    /** @test */
    public function it_can_find_most_similar_embeddings()
    {
        $queryText = 'What is Laravel?';
        $queryEmbedding = $this->embeddingService->generateEmbedding($queryText);
        
        $candidates = [
            [
                'id' => 1,
                'text' => 'Laravel is a PHP framework',
                'embedding' => $this->embeddingService->generateEmbedding('Laravel is a PHP framework')
            ],
            [
                'id' => 2,
                'text' => 'Python is a programming language',
                'embedding' => $this->embeddingService->generateEmbedding('Python is a programming language')
            ],
            [
                'id' => 3,
                'text' => 'Laravel framework for web development',
                'embedding' => $this->embeddingService->generateEmbedding('Laravel framework for web development')
            ]
        ];
        
        $results = $this->embeddingService->findMostSimilar($queryEmbedding, $candidates, 2);
        
        $this->assertCount(2, $results);
        $this->assertArrayHasKey('similarity', $results[0]);
        $this->assertArrayHasKey('id', $results[0]);
        $this->assertArrayHasKey('text', $results[0]);
        
        // Results should be sorted by similarity (highest first)
        $this->assertGreaterThanOrEqual($results[1]['similarity'], $results[0]['similarity']);
    }

    /** @test */
    public function it_can_normalize_text_properly()
    {
        $text = '  This is a TEST with   MIXED case and extra spaces!  ';
        $normalized = $this->embeddingService->normalizeText($text);
        
        $this->assertEquals('this is a test with mixed case and extra spaces!', $normalized);
    }

    /** @test */
    public function it_can_get_embedding_dimensions()
    {
        $dimensions = $this->embeddingService->getEmbeddingDimension();
        $this->assertEquals(384, $dimensions);
    }

    /** @test */
    public function it_can_batch_generate_embeddings()
    {
        $texts = [
            'First text to embed',
            'Second text to embed',
            'Third text to embed'
        ];
        
        $embeddings = $this->embeddingService->batchGenerateEmbeddings($texts);
        
        $this->assertCount(3, $embeddings);
        $this->assertIsArray($embeddings[0]);
        $this->assertCount(384, $embeddings[0]);
        $this->assertIsFloat($embeddings[0][0]);
    }

    /** @test */
    public function it_handles_empty_text_gracefully()
    {
        $embedding = $this->embeddingService->generateEmbedding('');
        $this->assertNull($embedding);
        
        $embedding = $this->embeddingService->generateEmbedding('   ');
        $this->assertNull($embedding);
    }

    /** @test */
    public function it_handles_very_long_text()
    {
        $longText = str_repeat('This is a very long text that exceeds normal limits. ', 100);
        $embedding = $this->embeddingService->generateEmbedding($longText);
        
        $this->assertIsArray($embedding);
        $this->assertCount(384, $embedding);
    }

    /** @test */
    public function identical_texts_have_perfect_similarity()
    {
        $text = 'Identical text for testing';
        $embedding1 = $this->embeddingService->generateEmbedding($text);
        $embedding2 = $this->embeddingService->generateEmbedding($text);
        
        $similarity = $this->embeddingService->calculateSimilarity($embedding1, $embedding2);
        $this->assertEquals(1.0, $similarity, '', 0.0001); // Allow for floating point precision
    }
}