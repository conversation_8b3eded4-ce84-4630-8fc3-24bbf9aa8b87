<?php

namespace App\Providers;

use App\Services\DeepSeekService;
use App\Services\GeminiService;
use App\Services\WiddxAiService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Http;

class WiddxServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register DeepSeek Service (uses config/services.php)
        $this->app->singleton(DeepSeekService::class, function ($app) {
            return new DeepSeekService();
        });

        // Register Gemini Service (uses config/services.php)
        $this->app->singleton(GeminiService::class, function ($app) {
            return new GeminiService();
        });

        // Register BrandingService
        $this->app->singleton(\App\Services\BrandingService::class, function ($app) {
            return new \App\Services\BrandingService();
        });

        // Register WIDDX AI Service with all dependencies
        $this->app->singleton(WiddxAiService::class, function ($app) {
            return new WiddxAiService(
                $app->make(DeepSeekService::class),
                $app->make(GeminiService::class),
                $app->make(\App\Services\LearningService::class),
                $app->make(\App\Services\BrandingService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration file
        $this->publishes([
            __DIR__.'/../../config/widdx.php' => config_path('widdx.php'),
        ], 'widdx-config');

        // Load configuration
        $this->mergeConfigFrom(
            __DIR__.'/../../config/widdx.php', 'widdx'
        );
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array<int, string>
     */
    public function provides(): array
    {
        return [
            DeepSeekService::class,
            GeminiService::class,
            WiddxAiService::class,
        ];
    }
}
