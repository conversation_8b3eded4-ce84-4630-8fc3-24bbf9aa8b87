<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;

class WiddxValidationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Validate content type
        if (!$this->isValidContentType($request)) {
            return response()->json([
                'error' => 'Invalid Content Type',
                'message' => 'Only application/json content type is allowed.',
            ], 415);
        }

        // Validate message content for chat endpoints
        if ($request->is('api/chat/message')) {
            $validation = $this->validateChatMessage($request);
            if ($validation !== true) {
                return $validation;
            }
        }

        // Sanitize input if enabled
        if (config('widdx.security.input_validation.sanitize_input', true)) {
            $this->sanitizeInput($request);
        }

        return $next($request);
    }

    /**
     * Check if the request has a valid content type.
     */
    protected function isValidContentType(Request $request): bool
    {
        $allowedTypes = config('widdx.security.input_validation.allowed_content_types', ['application/json']);
        $contentType = $request->header('Content-Type');
        
        // Extract base content type (remove charset, etc.)
        $baseContentType = explode(';', $contentType)[0] ?? '';
        
        return in_array(trim($baseContentType), $allowedTypes) || $request->isMethod('GET');
    }

    /**
     * Validate chat message request.
     */
    protected function validateChatMessage(Request $request)
    {
        $maxLength = config('widdx.security.input_validation.max_message_length', 4000);
        
        $validator = Validator::make($request->all(), [
            'message' => [
                'required',
                'string',
                'max:' . $maxLength,
                function ($attribute, $value, $fail) {
                    // Check for potentially malicious content
                    if ($this->containsMaliciousContent($value)) {
                        $fail('The message contains potentially harmful content.');
                    }
                },
            ],
            'session_id' => 'nullable|string|max:255',
            'conversation_id' => 'nullable|string|max:255',
            'language' => 'nullable|string|in:en,ar',
            'context' => 'nullable|array',
            'context.*' => 'string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation Error',
                'message' => 'The provided data is invalid.',
                'errors' => $validator->errors(),
            ], 422);
        }

        return true;
    }

    /**
     * Sanitize request input.
     */
    protected function sanitizeInput(Request $request): void
    {
        $input = $request->all();
        
        array_walk_recursive($input, function (&$value) {
            if (is_string($value)) {
                // Remove potentially dangerous characters
                $value = strip_tags($value);
                $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                // Remove null bytes
                $value = str_replace("\0", '', $value);
                // Trim whitespace
                $value = trim($value);
            }
        });
        
        $request->replace($input);
    }

    /**
     * Check for potentially malicious content.
     */
    protected function containsMaliciousContent(string $content): bool
    {
        $maliciousPatterns = [
            // SQL injection patterns
            '/\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\s+/i',
            // Script injection patterns
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload\s*=/i',
            '/onerror\s*=/i',
            // Command injection patterns
            '/[;&|`$(){}\[\]]/'
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }
        
        return false;
    }
}
