/* WIDDX AI - Elite States & Animations */
/* Advanced UI States and Micro-interactions */

/* Elite Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-tertiary);
    text-align: center;
    padding: 80px var(--content-padding);
    position: relative;
}

.empty-state::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    background: var(--brand-gradient);
    border-radius: 50%;
    opacity: 0.05;
    transform: translate(-50%, -50%);
    animation: pulseGlow 4s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.05; }
    50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.1; }
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius-2xl);
    background: var(--brand-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 32px;
    box-shadow: var(--shadow-2xl);
    position: relative;
    z-index: 1;
    animation: floatIcon 3s ease-in-out infinite;
}

@keyframes floatIcon {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.empty-state-icon i {
    font-size: 36px;
    color: var(--text-inverse);
}

.empty-state h4 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: 12px;
    letter-spacing: -0.03em;
    position: relative;
    z-index: 1;
}

.empty-state p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin: 0;
    max-width: 480px;
    line-height: var(--line-height-relaxed);
    position: relative;
    z-index: 1;
}

/* Elite Error Alert */
.error-alert {
    margin: 20px;
    border-radius: var(--border-radius-lg);
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border: 2px solid #fecaca;
    color: #dc2626;
    padding: 16px 20px;
    font-size: var(--font-size-base);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.error-alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #dc2626;
}

/* Elite Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid var(--brand-primary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Elite Utility Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.4s var(--transition-bounce);
}

@keyframes slideUp {
    from { 
        opacity: 0; 
        transform: translateY(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

.scale-in {
    animation: scaleIn 0.3s var(--transition-bounce);
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Elite Performance Optimizations */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* Hover State Enhancements */
.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Focus State Enhancements */
.focus-ring:focus {
    box-shadow: var(--focus-ring);
}

.focus-ring-error:focus {
    box-shadow: var(--focus-ring-error);
}

.focus-ring-success:focus {
    box-shadow: var(--focus-ring-success);
}

/* Active State Enhancements */
.active-press:active {
    transform: scale(0.98);
}

.active-sink:active {
    transform: translateY(1px);
    box-shadow: var(--shadow-sm);
}

/* Disabled State */
.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Selection States */
.selected {
    background: var(--brand-light);
    border-color: var(--brand-primary);
    color: var(--brand-secondary);
}

/* Notification States */
.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 8px;
    height: 8px;
    background: #ef4444;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* Success States */
.success {
    color: #059669;
    background: #ecfdf5;
    border-color: #a7f3d0;
}

/* Warning States */
.warning {
    color: #d97706;
    background: #fffbeb;
    border-color: #fde68a;
}

/* Info States */
.info {
    color: var(--brand-primary);
    background: var(--brand-lighter);
    border-color: var(--brand-light);
}
