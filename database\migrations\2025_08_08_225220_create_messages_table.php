<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conversation_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['user', 'ai', 'system'])->default('user');
            $table->longText('content');
            $table->string('language', 10)->default('en');
            $table->decimal('processing_time', 8, 2)->nullable();
            $table->integer('sources_used')->default(0);
            $table->decimal('quality_score', 3, 2)->nullable();
            $table->json('model_info')->nullable();
            $table->json('metadata')->nullable();
            $table->json('error_info')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['conversation_id', 'type']);
            $table->index(['language']);
            $table->index(['quality_score']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
