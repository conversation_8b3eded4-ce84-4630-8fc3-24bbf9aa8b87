# WIDDX AI Self-Learning System

The WIDDX AI platform now includes a sophisticated self-learning system that builds a knowledge base from user interactions, reducing API costs and improving response times through intelligent caching and similarity matching.

## 🚀 Features

### Core Capabilities
- **Intelligent Knowledge Base**: Stores Q&A pairs with vector embeddings for semantic similarity search
- **Multiple Embedding Providers**: Support for OpenAI, local models, and Sentence Transformers
- **Automatic Learning**: Learns from every user interaction when enabled
- **Quality Management**: Tracks response quality and user feedback
- **Smart Pruning**: Automatically removes low-quality or outdated entries
- **Multi-language Support**: Handles different languages with proper isolation
- **API Cost Reduction**: Serves answers from knowledge base before making external API calls

### Performance Benefits
- **Faster Responses**: Instant answers from knowledge base (no API latency)
- **Cost Savings**: Reduces external API calls by serving cached responses
- **Improved Accuracy**: Learns from successful interactions over time
- **Scalability**: Grows smarter with usage

## 📋 System Architecture

### Core Services

#### 1. EmbeddingService (`app/Services/EmbeddingService.php`)
- Generates vector embeddings for text similarity
- Supports multiple providers (OpenAI, local, Sentence Transformers)
- Handles embedding caching and normalization
- Calculates cosine similarity between vectors

#### 2. SimilaritySearchService (`app/Services/SimilaritySearchService.php`)
- Performs semantic search in the knowledge base
- Combines exact hash matching with embedding-based similarity
- Manages candidate filtering and result ranking
- Rebuilds missing embeddings

#### 3. LearningService (`app/Services/LearningService.php`)
- Orchestrates the learning pipeline
- Stores new Q&A pairs with quality scoring
- Manages knowledge base pruning and statistics
- Handles user feedback integration

### Database Schema

#### KnowledgeBase Model
```sql
CREATE TABLE knowledge_bases (
    id BIGINT PRIMARY KEY,
    question_hash VARCHAR(64) UNIQUE,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    language VARCHAR(10) DEFAULT 'en',
    embedding JSON,
    confidence_score DECIMAL(5,4),
    usage_count INT DEFAULT 0,
    quality_rating DECIMAL(3,2) DEFAULT 0.5,
    source_model VARCHAR(50),
    last_used_at TIMESTAMP,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## ⚙️ Configuration

### Environment Variables
Add these to your `.env` file:

```env
# Embedding Provider Configuration
WIDDX_EMBEDDING_PROVIDER=openai
WIDDX_OPENAI_EMBEDDING_MODEL=text-embedding-3-small
WIDDX_SENTENCE_TRANSFORMER_MODEL=all-MiniLM-L6-v2

# Feature Flags
WIDDX_SELF_LEARNING_ENABLED=true
WIDDX_SIMILARITY_SEARCH_ENABLED=true
WIDDX_KNOWLEDGE_BASE_ENABLED=true

# Learning Parameters
WIDDX_LEARNING_AUTO_LEARN=true
WIDDX_LEARNING_MIN_CONFIDENCE=0.7
WIDDX_SIMILARITY_THRESHOLD=0.8
WIDDX_SIMILARITY_MAX_RESULTS=5
```

### Configuration File
The system is configured in `config/widdx.php` with the following sections:

- **embedding**: Provider settings, API keys, models, dimensions
- **similarity**: Search thresholds, result limits, matching options
- **learning**: Auto-learning, confidence levels, quality management
- **knowledge_base**: Priority settings, fallback behavior, context matching

## 🔧 Usage

### API Endpoints

#### Chat with Learning
```http
POST /api/chat/send
Content-Type: application/json

{
    "message": "What is Laravel?",
    "conversation_id": "optional-uuid"
}
```

#### Rate Response Quality
```http
POST /api/chat/rate-response
Content-Type: application/json

{
    "message_id": 123,
    "rating": 0.9,
    "feedback": "Very helpful answer"
}
```

#### Get Learning Statistics
```http
GET /api/chat/learning-stats
```

#### Prune Knowledge Base
```http
POST /api/chat/prune-knowledge-base
Content-Type: application/json

{
    "min_quality": 0.3,
    "max_age_days": 90,
    "min_usage_count": 1
}
```

### Command Line Management

#### View Statistics
```bash
php artisan knowledge-base:manage stats
```

#### Search Knowledge Base
```bash
php artisan knowledge-base:manage search --query="Laravel validation" --language=en --limit=10
```

#### Prune Low-Quality Entries
```bash
php artisan knowledge-base:manage prune --min-quality=0.3 --max-age=90 --min-usage=1 --force
```

#### Rebuild Missing Embeddings
```bash
php artisan knowledge-base:manage rebuild
```

#### Export Knowledge Base
```bash
php artisan knowledge-base:manage export --language=en
```

## 🔄 Learning Workflow

### 1. Query Processing
1. User sends a question via chat API
2. System checks knowledge base for similar questions
3. If found with high confidence, returns cached answer
4. If not found, queries external APIs (DeepSeek + Gemini)
5. Stores new Q&A pair in knowledge base with embeddings

### 2. Quality Management
1. Initial quality score based on confidence and model performance
2. User feedback updates quality ratings
3. Usage statistics track popular questions
4. Automatic pruning removes low-quality entries

### 3. Similarity Matching
1. Exact hash matching for identical questions
2. Embedding-based semantic similarity for related questions
3. Configurable similarity thresholds
4. Multi-language support with language isolation

## 📊 Monitoring and Analytics

### Learning Statistics
- Total knowledge base entries
- API calls saved through caching
- Average quality ratings
- Usage patterns by language
- Quality distribution analysis

### Performance Metrics
- Response time improvements
- Cache hit rates
- Cost savings calculations
- User satisfaction scores

## 🛠️ Customization

### Adding New Embedding Providers
1. Extend `EmbeddingService` with new provider logic
2. Add configuration options in `config/widdx.php`
3. Update environment variables

### Custom Quality Scoring
1. Modify `LearningService::calculateQualityScore()`
2. Implement custom scoring algorithms
3. Add new quality metrics

### Advanced Similarity Algorithms
1. Extend `SimilaritySearchService` with new algorithms
2. Implement custom distance metrics
3. Add semantic preprocessing steps

## 🔒 Security Considerations

- **Input Validation**: All user inputs are sanitized and validated
- **Rate Limiting**: API endpoints include rate limiting
- **Data Privacy**: Knowledge base entries can be pruned based on age
- **Access Control**: Admin commands require appropriate permissions
- **Embedding Security**: API keys are securely stored and managed

## 🚀 Performance Optimization

### Database Optimization
- Indexes on `question_hash`, `language`, and `quality_rating`
- JSON indexing for embedding vectors (MySQL 8.0+)
- Partitioning by language for large datasets

### Caching Strategy
- Embedding results cached with configurable TTL
- Similarity search results cached for frequent queries
- Redis integration for distributed caching

### Batch Processing
- Bulk embedding generation for efficiency
- Batch pruning operations
- Asynchronous learning pipeline

## 📈 Future Enhancements

- **Active Learning**: Identify knowledge gaps and suggest improvements
- **Federated Learning**: Share knowledge across multiple instances
- **Advanced Analytics**: Machine learning insights on usage patterns
- **Real-time Updates**: Live knowledge base updates
- **Multi-modal Support**: Image and document embeddings

## 🐛 Troubleshooting

### Common Issues

#### Embedding Generation Fails
- Check API keys and provider configuration
- Verify network connectivity
- Review rate limits and quotas

#### Low Similarity Scores
- Adjust similarity thresholds in configuration
- Consider different embedding models
- Review text preprocessing steps

#### Performance Issues
- Check database indexes
- Monitor embedding cache hit rates
- Consider batch processing for large datasets

### Debug Commands
```bash
# Check system health
php artisan widdx:health

# View detailed logs
tail -f storage/logs/laravel.log

# Test embedding generation
php artisan tinker
>>> app(App\Services\EmbeddingService::class)->generateEmbedding('test query')
```

## 📝 Contributing

When contributing to the self-learning system:

1. **Test Coverage**: Ensure new features have comprehensive tests
2. **Documentation**: Update this README and inline documentation
3. **Performance**: Consider impact on response times and resource usage
4. **Security**: Review for potential vulnerabilities
5. **Backward Compatibility**: Maintain compatibility with existing APIs

---

*The WIDDX AI Self-Learning System represents a significant advancement in AI-powered applications, providing intelligent caching, cost optimization, and continuous improvement through user interactions.*