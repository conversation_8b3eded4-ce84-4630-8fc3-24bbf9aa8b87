/* WIDDX AI - Elite Base Styles & Reset */
/* Modern CSS Reset & Foundation */

/* Advanced Reset & Base Styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
    background: var(--bg-secondary);
    color: var(--text-primary);
    height: 100vh;
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Typography Base */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: -0.025em;
}

p {
    line-height: var(--line-height-normal);
}

/* Button Reset */
button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    color: inherit;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    margin: 0;
}

/* Input Reset */
input, textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    color: inherit;
    background: transparent;
    border: none;
    outline: none;
    padding: 0;
    margin: 0;
}

/* Focus Management */
:focus {
    outline: none;
}

.focus-visible {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

/* Selection Styles */
::selection {
    background: var(--brand-light);
    color: var(--brand-secondary);
}

::-moz-selection {
    background: var(--brand-light);
    color: var(--brand-secondary);
}

/* Scrollbar Base Styles */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
    border-radius: var(--border-radius-full);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-full);
    transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-quaternary);
}

/* Firefox Scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--bg-tertiary) transparent;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
        --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    }
}

/* Print Styles */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.5;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    p {
        orphans: 3;
        widows: 3;
    }
    
    img {
        max-width: 100% !important;
    }
}
