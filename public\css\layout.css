/* WIDDX AI - Elite Layout System */
/* Advanced Layout Components */

/* Elite Layout System */
.chat-layout {
    display: flex;
    height: 100vh;
    background: var(--bg-secondary);
    position: relative;
}

/* Elite Sidebar Design */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-sidebar);
    border-right: 1px solid var(--bg-tertiary);
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 100;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-glass);
    z-index: -1;
}

/* Elite Sidebar Header */
.sidebar-header {
    padding: var(--content-padding);
    background: var(--brand-gradient);
    color: var(--text-inverse);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.sidebar-header:hover::before {
    left: 100%;
}

.sidebar-header i {
    font-size: 24px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Elite Conversations List */
.conversations-list {
    flex: 1;
    overflow-y: auto;
    padding: 12px 16px;
    scrollbar-width: thin;
    scrollbar-color: var(--bg-tertiary) transparent;
}

.conversations-list::-webkit-scrollbar {
    width: 6px;
}

.conversations-list::-webkit-scrollbar-track {
    background: transparent;
    border-radius: var(--border-radius-full);
}

.conversations-list::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-full);
    transition: background var(--transition-fast);
}

.conversations-list::-webkit-scrollbar-thumb:hover {
    background: var(--text-quaternary);
}

/* Elite Main Chat Area */
.main-chat {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-chat);
    position: relative;
    max-width: 100%;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* Elite Chat Header */
.chat-header {
    height: var(--header-height);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--content-padding);
    box-shadow: var(--shadow-md);
    position: relative;
    z-index: 50;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.chat-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-glass);
    z-index: -1;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Elite Chat Messages Area */
.chat-messages {
    flex: 1;
    padding: 0;
    overflow-y: auto;
    background: var(--bg-chat);
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: var(--bg-tertiary) transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.chat-messages::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(29, 78, 216, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
    border-radius: var(--border-radius-full);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-full);
    transition: background var(--transition-fast);
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--text-quaternary);
}

.messages-container {
    width: 100%;
    max-width: var(--max-width);
    padding: var(--content-padding);
    position: relative;
    z-index: 1;
}

/* Elite Input Container */
.chat-input-container {
    background: var(--bg-primary);
    border-top: 1px solid var(--bg-tertiary);
    padding: var(--content-padding);
    display: flex;
    justify-content: center;
    position: relative;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.chat-input-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-glass);
    z-index: -1;
}

.input-wrapper {
    width: 100%;
    max-width: var(--max-width);
    display: flex;
    align-items: flex-end;
    gap: 16px;
    position: relative;
    z-index: 1;
}

/* Elite Sidebar Overlay */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 999;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: all var(--transition-normal);
}

.sidebar-overlay.show {
    display: block;
}
