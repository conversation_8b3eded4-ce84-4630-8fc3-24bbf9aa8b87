<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>WIDDX AI Chat</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        /* WIDDX AI - Elite Chat Interface 2025 */
        /* World-Class Design System */
        :root {
            /* Elite Color Palette - Light Theme */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --bg-quaternary: #e2e8f0;
            --bg-chat: #ffffff;
            --bg-input: #ffffff;
            --bg-sidebar: #fafbfc;
            --bg-glass: rgba(255, 255, 255, 0.8);
            --bg-overlay: rgba(0, 0, 0, 0.05);

            /* Advanced Text Hierarchy */
            --text-primary: #0f172a;
            --text-secondary: #334155;
            --text-tertiary: #64748b;
            --text-quaternary: #94a3b8;
            --text-muted: #cbd5e1;
            --text-inverse: #ffffff;
            --text-accent: #1e40af;

            /* Sophisticated Brand System */
            --brand-primary: #3b82f6;
            --brand-secondary: #1d4ed8;
            --brand-tertiary: #1e40af;
            --brand-light: #dbeafe;
            --brand-lighter: #eff6ff;
            --brand-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
            --brand-gradient-subtle: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #93c5fd 100%);
            --brand-gradient-reverse: linear-gradient(315deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);

            /* Elite Message System */
            --user-bg: var(--brand-gradient);
            --user-bg-hover: linear-gradient(135deg, #2563eb 0%, #1d4ed8 50%, #1e40af 100%);
            --user-text: #ffffff;
            --user-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
            --ai-bg: #f8fafc;
            --ai-bg-hover: #f1f5f9;
            --ai-text: var(--text-primary);
            --ai-border: #e2e8f0;
            --ai-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

            /* Advanced Interactive States */
            --hover-bg: #f8fafc;
            --hover-border: #cbd5e1;
            --active-bg: #f1f5f9;
            --active-border: #94a3b8;
            --focus-ring: 0 0 0 3px rgba(59, 130, 246, 0.12);
            --focus-ring-error: 0 0 0 3px rgba(239, 68, 68, 0.12);
            --focus-ring-success: 0 0 0 3px rgba(34, 197, 94, 0.12);

            /* Elite Shadow System */
            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);

            /* Premium Layout System */
            --sidebar-width: 280px;
            --header-height: 64px;
            --input-container-height: 88px;
            --border-radius-xs: 4px;
            --border-radius-sm: 6px;
            --border-radius: 8px;
            --border-radius-md: 10px;
            --border-radius-lg: 12px;
            --border-radius-xl: 16px;
            --border-radius-2xl: 20px;
            --border-radius-3xl: 24px;
            --border-radius-full: 9999px;
            --max-width: 800px;
            --content-padding: 24px;

            /* Advanced Typography Scale */
            --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui, "Helvetica Neue", "Noto Sans", sans-serif;
            --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, monospace;
            --font-size-xs: 11px;
            --font-size-sm: 12px;
            --font-size-base: 14px;
            --font-size-lg: 16px;
            --font-size-xl: 18px;
            --font-size-2xl: 20px;
            --font-size-3xl: 24px;
            --font-size-4xl: 28px;
            --line-height-tight: 1.25;
            --line-height-normal: 1.5;
            --line-height-relaxed: 1.75;
            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;

            /* Animation & Transition System */
            --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            --transition-elastic: 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        /* Elite Dark Theme */
        [data-theme="dark"] {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --bg-quaternary: #475569;
            --bg-chat: #0f172a;
            --bg-input: #1e293b;
            --bg-sidebar: #020617;
            --bg-glass: rgba(15, 23, 42, 0.8);
            --bg-overlay: rgba(255, 255, 255, 0.05);

            --text-primary: #f8fafc;
            --text-secondary: #e2e8f0;
            --text-tertiary: #cbd5e1;
            --text-quaternary: #94a3b8;
            --text-muted: #64748b;
            --text-inverse: #0f172a;
            --text-accent: #60a5fa;

            --brand-gradient: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
            --brand-gradient-subtle: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #1d4ed8 100%);

            --ai-bg: #1e293b;
            --ai-bg-hover: #334155;
            --ai-border: #475569;
            --ai-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

            --hover-bg: #1e293b;
            --hover-border: #475569;
            --active-bg: #334155;
            --active-border: #64748b;
            --focus-ring: 0 0 0 3px rgba(96, 165, 250, 0.2);

            --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px -1px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -2px rgba(0, 0, 0, 0.3);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -4px rgba(0, 0, 0, 0.3);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 8px 10px -6px rgba(0, 0, 0, 0.4);
        }

        /* Advanced Reset & Base Styles */
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            scroll-behavior: smooth;
            text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }

        body {
            font-family: var(--font-family);
            font-size: var(--font-size-base);
            line-height: var(--line-height-normal);
            font-weight: var(--font-weight-normal);
            background: var(--bg-secondary);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Elite Layout System */
        .chat-layout {
            display: flex;
            height: 100vh;
            background: var(--bg-secondary);
            position: relative;
        }

        /* Elite Sidebar Design */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--bg-sidebar);
            border-right: 1px solid var(--bg-tertiary);
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 100;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-glass);
            z-index: -1;
        }

        /* Elite Sidebar Header */
        .sidebar-header {
            padding: var(--content-padding);
            background: var(--brand-gradient);
            color: var(--text-inverse);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            font-weight: var(--font-weight-semibold);
            font-size: var(--font-size-lg);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left var(--transition-slow);
        }

        .sidebar-header:hover::before {
            left: 100%;
        }

        .sidebar-header i {
            font-size: 24px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Elite New Chat Button */
        .new-chat-btn {
            margin: 20px;
            background: var(--bg-primary);
            border: 1px solid var(--bg-tertiary);
            color: var(--text-primary);
            padding: 16px 20px;
            border-radius: var(--border-radius-xl);
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-base);
            transition: all var(--transition-normal);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            cursor: pointer;
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .new-chat-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--brand-gradient-subtle);
            transition: left var(--transition-normal);
            z-index: -1;
        }

        .new-chat-btn:hover {
            background: var(--hover-bg);
            border-color: var(--brand-primary);
            color: var(--brand-primary);
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .new-chat-btn:hover::before {
            left: 0;
        }

        .new-chat-btn:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }

        .new-chat-btn i {
            transition: transform var(--transition-normal);
        }

        .new-chat-btn:hover i {
            transform: rotate(90deg);
        }

        /* Elite Conversations List */
        .conversations-list {
            flex: 1;
            overflow-y: auto;
            padding: 12px 16px;
            scrollbar-width: thin;
            scrollbar-color: var(--bg-tertiary) transparent;
        }

        .conversations-list::-webkit-scrollbar {
            width: 6px;
        }

        .conversations-list::-webkit-scrollbar-track {
            background: transparent;
            border-radius: var(--border-radius-full);
        }

        .conversations-list::-webkit-scrollbar-thumb {
            background: var(--bg-tertiary);
            border-radius: var(--border-radius-full);
            transition: background var(--transition-fast);
        }

        .conversations-list::-webkit-scrollbar-thumb:hover {
            background: var(--text-quaternary);
        }

        /* Elite Conversation Items */
        .conversation-item {
            padding: 16px 20px;
            margin-bottom: 4px;
            border-radius: var(--border-radius-lg);
            cursor: pointer;
            transition: all var(--transition-normal);
            border: 1px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .conversation-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--brand-gradient-subtle);
            transition: left var(--transition-normal);
            z-index: -1;
            opacity: 0.1;
        }

        .conversation-item:hover {
            background: var(--hover-bg);
            border-color: var(--bg-tertiary);
            box-shadow: var(--shadow-sm);
            transform: translateX(4px);
        }

        .conversation-item:hover::before {
            left: 0;
        }

        .conversation-item.active {
            background: var(--brand-light);
            border-color: var(--brand-primary);
            color: var(--brand-secondary);
            box-shadow: var(--shadow-md);
            transform: translateX(8px);
        }

        .conversation-item.active::before {
            left: 0;
            opacity: 0.2;
        }

        [data-theme="dark"] .conversation-item.active {
            background: rgba(59, 130, 246, 0.1);
            border-color: var(--brand-primary);
            color: var(--text-accent);
        }

        .conversation-title {
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-sm);
            margin-bottom: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: var(--line-height-tight);
        }

        .conversation-preview {
            font-size: var(--font-size-xs);
            color: var(--text-tertiary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: var(--line-height-tight);
        }

        .conversation-time {
            font-size: var(--font-size-xs);
            color: var(--text-quaternary);
            margin-top: 6px;
            font-weight: var(--font-weight-normal);
        }

        /* Elite Main Chat Area */
        .main-chat {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg-chat);
            position: relative;
            max-width: 100%;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        /* Elite Chat Header */
        .chat-header {
            height: var(--header-height);
            background: var(--bg-primary);
            border-bottom: 1px solid var(--bg-tertiary);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--content-padding);
            box-shadow: var(--shadow-md);
            position: relative;
            z-index: 50;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .chat-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-glass);
            z-index: -1;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .mobile-toggle {
            display: none;
            background: none;
            border: none;
            font-size: var(--font-size-lg);
            color: var(--text-secondary);
            cursor: pointer;
            padding: 10px;
            border-radius: var(--border-radius-lg);
            transition: all var(--transition-normal);
        }

        .mobile-toggle:hover {
            background: var(--hover-bg);
            color: var(--text-primary);
            transform: scale(1.1);
        }

        .chat-title {
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0;
            font-size: var(--font-size-lg);
            letter-spacing: -0.02em;
            background: var(--brand-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Elite Chat Messages Area */
        .chat-messages {
            flex: 1;
            padding: 0;
            overflow-y: auto;
            background: var(--bg-chat);
            scroll-behavior: smooth;
            scrollbar-width: thin;
            scrollbar-color: var(--bg-tertiary) transparent;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .chat-messages::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(29, 78, 216, 0.03) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: transparent;
            border-radius: var(--border-radius-full);
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: var(--bg-tertiary);
            border-radius: var(--border-radius-full);
            transition: background var(--transition-fast);
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: var(--text-quaternary);
        }

        .messages-container {
            width: 100%;
            max-width: var(--max-width);
            padding: var(--content-padding);
            position: relative;
            z-index: 1;
        }

        /* Elite Message Cards */
        .message-card {
            margin-bottom: 32px;
            display: flex;
            flex-direction: column;
            position: relative;
            animation: messageSlideIn 0.4s var(--transition-bounce);
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message-card.user {
            align-items: flex-end;
        }

        .message-card.ai {
            align-items: flex-start;
        }

        .message-body {
            max-width: 85%;
            padding: 20px 24px;
            border-radius: var(--border-radius-2xl);
            font-size: var(--font-size-base);
            line-height: var(--line-height-relaxed);
            word-wrap: break-word;
            position: relative;
            transition: all var(--transition-normal);
        }

        .message-card.user .message-body {
            background: var(--user-bg);
            color: var(--user-text);
            box-shadow: var(--user-shadow);
            border-bottom-right-radius: var(--border-radius-sm);
        }

        .message-card.user .message-body:hover {
            background: var(--user-bg-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .message-card.ai .message-body {
            background: var(--ai-bg);
            color: var(--ai-text);
            border: 1px solid var(--ai-border);
            box-shadow: var(--ai-shadow);
            border-bottom-left-radius: var(--border-radius-sm);
        }

        .message-card.ai .message-body:hover {
            background: var(--ai-bg-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .message-time {
            font-size: var(--font-size-xs);
            color: var(--text-quaternary);
            margin-top: 8px;
            font-weight: var(--font-weight-normal);
            padding: 0 6px;
            opacity: 0.8;
        }

        /* Elite Input Container */
        .chat-input-container {
            background: var(--bg-primary);
            border-top: 1px solid var(--bg-tertiary);
            padding: var(--content-padding);
            display: flex;
            justify-content: center;
            position: relative;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .chat-input-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-glass);
            z-index: -1;
        }

        .input-wrapper {
            width: 100%;
            max-width: var(--max-width);
            display: flex;
            align-items: flex-end;
            gap: 16px;
            position: relative;
            z-index: 1;
        }

        /* Elite Message Input */
        .message-input {
            flex: 1;
            border: 2px solid var(--bg-tertiary);
            border-radius: var(--border-radius-2xl);
            padding: 18px 24px;
            font-size: var(--font-size-base);
            outline: none;
            transition: all var(--transition-normal);
            background: var(--bg-input);
            color: var(--text-primary);
            resize: none;
            min-height: 56px;
            max-height: 140px;
            font-family: var(--font-family);
            line-height: var(--line-height-normal);
            box-shadow: var(--shadow-sm);
            position: relative;
        }

        .message-input::placeholder {
            color: var(--text-quaternary);
            font-weight: var(--font-weight-normal);
        }

        .message-input:focus {
            border-color: var(--brand-primary);
            box-shadow: var(--focus-ring), var(--shadow-md);
            background: var(--bg-primary);
            transform: translateY(-2px);
        }

        .message-input:hover:not(:focus) {
            border-color: var(--hover-border);
            box-shadow: var(--shadow-md);
        }

        /* Elite Send Button */
        .send-btn {
            width: 56px;
            height: 56px;
            border-radius: var(--border-radius-2xl);
            background: var(--brand-gradient);
            border: none;
            color: var(--text-inverse);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-normal);
            cursor: pointer;
            font-size: var(--font-size-lg);
            box-shadow: var(--shadow-md);
            flex-shrink: 0;
            position: relative;
            overflow: hidden;
        }

        .send-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left var(--transition-normal);
        }

        .send-btn:hover:not(:disabled) {
            transform: translateY(-3px) scale(1.05);
            box-shadow: var(--shadow-xl);
            background: var(--brand-gradient-reverse);
        }

        .send-btn:hover:not(:disabled)::before {
            left: 100%;
        }

        .send-btn:active:not(:disabled) {
            transform: translateY(-1px) scale(1.02);
            box-shadow: var(--shadow-lg);
        }

        .send-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none;
            box-shadow: var(--shadow-sm);
        }

        .send-btn i {
            transition: transform var(--transition-normal);
        }

        .send-btn:hover:not(:disabled) i {
            transform: translateX(2px);
        }

        /* Elite Typing Indicator */
        .typing-indicator {
            display: none;
            padding: 20px;
            color: var(--text-tertiary);
            background: var(--bg-primary);
            border-top: 1px solid var(--bg-tertiary);
            font-size: var(--font-size-sm);
            justify-content: center;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .typing-indicator.show {
            display: flex;
        }

        .typing-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-glass);
            z-index: -1;
        }

        .typing-content {
            max-width: var(--max-width);
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 0 var(--content-padding);
            position: relative;
            z-index: 1;
        }

        .typing-dots {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: var(--brand-primary);
            animation: typingElite 1.6s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        .typing-dot:nth-child(3) { animation-delay: 0s; }

        @keyframes typingElite {
            0%, 80%, 100% {
                transform: scale(0.5) translateY(0);
                opacity: 0.4;
            }
            40% {
                transform: scale(1.2) translateY(-8px);
                opacity: 1;
            }
        }

        /* Elite Empty State */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--text-tertiary);
            text-align: center;
            padding: 80px var(--content-padding);
            position: relative;
        }

        .empty-state::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 300px;
            height: 300px;
            background: var(--brand-gradient);
            border-radius: 50%;
            opacity: 0.05;
            transform: translate(-50%, -50%);
            animation: pulseGlow 4s ease-in-out infinite;
        }

        @keyframes pulseGlow {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.05; }
            50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.1; }
        }

        .empty-state-icon {
            width: 80px;
            height: 80px;
            border-radius: var(--border-radius-2xl);
            background: var(--brand-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 32px;
            box-shadow: var(--shadow-2xl);
            position: relative;
            z-index: 1;
            animation: floatIcon 3s ease-in-out infinite;
        }

        @keyframes floatIcon {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .empty-state-icon i {
            font-size: 36px;
            color: var(--text-inverse);
        }

        .empty-state h4 {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: 12px;
            letter-spacing: -0.03em;
            position: relative;
            z-index: 1;
        }

        .empty-state p {
            font-size: var(--font-size-lg);
            color: var(--text-secondary);
            margin: 0;
            max-width: 480px;
            line-height: var(--line-height-relaxed);
            position: relative;
            z-index: 1;
        }

        /* Elite Theme Toggle */
        .theme-toggle {
            background: var(--bg-primary);
            border: 2px solid var(--bg-tertiary);
            border-radius: var(--border-radius-lg);
            padding: 12px;
            cursor: pointer;
            transition: all var(--transition-normal);
            color: var(--text-secondary);
            font-size: var(--font-size-lg);
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .theme-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--brand-gradient-subtle);
            transition: left var(--transition-normal);
            z-index: -1;
        }

        .theme-toggle:hover {
            background: var(--hover-bg);
            color: var(--brand-primary);
            box-shadow: var(--shadow-md);
            transform: scale(1.1);
            border-color: var(--brand-primary);
        }

        .theme-toggle:hover::before {
            left: 0;
        }

        .theme-toggle i {
            transition: transform var(--transition-normal);
        }

        .theme-toggle:hover i {
            transform: rotate(180deg);
        }

        /* Elite Error Alert */
        .error-alert {
            margin: 20px;
            border-radius: var(--border-radius-lg);
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #fecaca;
            color: #dc2626;
            padding: 16px 20px;
            font-size: var(--font-size-base);
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .error-alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: #dc2626;
        }

        [data-theme="dark"] .error-alert {
            background: linear-gradient(135deg, #450a0a 0%, #7f1d1d 100%);
            border-color: #7f1d1d;
            color: #fca5a5;
        }

        /* Elite Mobile Responsive Design */
        @media (max-width: 768px) {
            :root {
                --sidebar-width: 300px;
                --max-width: 100%;
                --content-padding: 16px;
                --header-height: 56px;
            }

            .sidebar {
                position: fixed;
                left: -100%;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transition: left var(--transition-slow);
                box-shadow: var(--shadow-2xl);
            }

            .sidebar.show {
                left: 0;
            }

            .main-chat {
                width: 100%;
            }

            .message-body {
                max-width: 92%;
                padding: 16px 20px;
            }

            .mobile-toggle {
                display: block !important;
            }

            .messages-container {
                padding: var(--content-padding);
            }

            .chat-input-container {
                padding: var(--content-padding);
            }

            .input-wrapper {
                gap: 12px;
            }

            .message-input {
                min-height: 48px;
                padding: 14px 18px;
                font-size: var(--font-size-base);
            }

            .send-btn {
                width: 48px;
                height: 48px;
                font-size: var(--font-size-base);
            }

            .empty-state {
                padding: 60px var(--content-padding);
            }

            .empty-state-icon {
                width: 64px;
                height: 64px;
                margin-bottom: 24px;
            }

            .empty-state-icon i {
                font-size: 28px;
            }

            .empty-state h4 {
                font-size: var(--font-size-2xl);
            }

            .empty-state p {
                font-size: var(--font-size-base);
            }

            .chat-title {
                font-size: var(--font-size-base);
            }

            .new-chat-btn {
                margin: 16px;
                padding: 14px 18px;
            }

            .conversation-item {
                padding: 14px 16px;
            }
        }

        /* Elite Sidebar Overlay */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 999;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            transition: all var(--transition-normal);
        }

        @media (max-width: 768px) {
            .sidebar-overlay.show {
                display: block;
            }
        }

        /* Elite Accessibility & Focus States */
        .focus-visible {
            outline: 2px solid var(--brand-primary);
            outline-offset: 2px;
        }

        /* Elite Loading States */
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            border: 2px solid var(--brand-primary);
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            transform: translate(-50%, -50%);
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Elite Utility Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .slide-up {
            animation: slideUp 0.4s var(--transition-bounce);
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Elite Performance Optimizations */
        .gpu-accelerated {
            transform: translateZ(0);
            will-change: transform;
        }

        /* Elite Print Styles */
        @media print {
            .sidebar,
            .chat-input-container,
            .typing-indicator,
            .theme-toggle {
                display: none !important;
            }

            .main-chat {
                width: 100% !important;
            }

            .message-body {
                box-shadow: none !important;
                border: 1px solid #ccc !important;
            }
        }
    </style>
</head>
<body>
    <div class="chat-layout">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <i class="bi bi-robot"></i> WIDDX AI
            </div>

            <button class="btn new-chat-btn" onclick="startNewConversation()">
                <i class="bi bi-plus-circle me-2"></i>New Conversation
            </button>

            <div class="conversations-list" id="conversationsList">
                <!-- Conversations will be loaded here -->
            </div>
        </div>

        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleSidebar()"></div>

        <!-- Main Chat Area -->
        <div class="main-chat">
            <div class="chat-header">
                <div class="header-left">
                    <button class="mobile-toggle" onclick="toggleSidebar()">
                        <i class="bi bi-list"></i>
                    </button>
                    <h5 class="chat-title" id="chatTitle">Welcome to WIDDX AI</h5>
                </div>
                <button class="theme-toggle" onclick="toggleTheme()" title="Toggle dark mode">
                    <i class="bi bi-moon-fill" id="themeIcon"></i>
                </button>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="messages-container">
                    <div class="empty-state" id="emptyState">
                        <div class="empty-state-icon">
                            <i class="bi bi-robot"></i>
                        </div>
                        <h4>Welcome to WIDDX AI</h4>
                        <p>Your intelligent assistant is ready to help. Start a conversation by typing a message below.</p>
                    </div>
                </div>
            </div>

            <div class="typing-indicator" id="typingIndicator">
                <div class="typing-content">
                    <span>WIDDX AI is thinking</span>
                    <div class="typing-dots">
                        <span class="typing-dot"></span>
                        <span class="typing-dot"></span>
                        <span class="typing-dot"></span>
                    </div>
                </div>
            </div>

            <div class="chat-input-container">
                <div class="input-wrapper">
                    <textarea class="message-input" id="messageInput"
                           placeholder="Type your message here..."
                           maxlength="4000"
                           rows="1"></textarea>
                    <button class="send-btn" id="sendButton" onclick="sendMessage()">
                        <i class="bi bi-send-fill"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        class WiddxChatApp {
            constructor() {
                this.currentConversationId = null;
                this.conversations = [];
                this.isProcessing = false;
                this.apiBaseUrl = '/api';

                this.initializeElements();
                this.bindEvents();
                this.loadConversations();
                this.initializeUI();
            }

            initializeElements() {
                this.chatMessages = document.getElementById('chatMessages');
                this.messagesContainer = this.chatMessages.querySelector('.messages-container');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.typingIndicator = document.getElementById('typingIndicator');
                this.conversationsList = document.getElementById('conversationsList');
                this.chatTitle = document.getElementById('chatTitle');
                this.emptyState = document.getElementById('emptyState');
                this.sidebar = document.getElementById('sidebar');
                this.sidebarOverlay = document.getElementById('sidebarOverlay');
            }

            bindEvents() {
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !this.isProcessing) {
                        this.sendMessage();
                    }
                });

                // Auto-resize input
                this.messageInput.addEventListener('input', () => {
                    this.messageInput.style.height = 'auto';
                    this.messageInput.style.height = this.messageInput.scrollHeight + 'px';
                });
            }

            initializeUI() {
                // Ensure typing indicator is hidden on startup
                this.hideTypingIndicator();

                // Ensure empty state is visible if no conversations
                if (this.conversations.length === 0) {
                    this.emptyState.style.display = 'flex';
                }
            }

            async loadConversations() {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/conversations`, {
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    });

                    if (response.ok) {
                        const payload = await response.json();
                        const list = (payload && payload.data && Array.isArray(payload.data)) ? payload.data : (payload.conversations || []);
                        this.conversations = list;
                        this.renderConversations();
                    }
                } catch (error) {
                    console.error('Failed to load conversations:', error);
                }
            }

            renderConversations() {
                this.conversationsList.innerHTML = '';

                this.conversations.forEach(conversation => {
                    const item = document.createElement('div');
                    item.className = `conversation-item ${conversation.id === this.currentConversationId ? 'active' : ''}`;
                    item.onclick = () => this.loadConversation(conversation.id);

                    item.innerHTML = `
                        <div class="conversation-title">${conversation.title || 'Untitled conversation'}</div>
                        <div class="conversation-preview">${(conversation.latest_message && conversation.latest_message.content) ? conversation.latest_message.content : ''}</div>
                        <div class="conversation-time">${this.formatTime(conversation.last_activity || conversation.created_at)}</div>
                    `;

                    this.conversationsList.appendChild(item);
                });
            }

            async loadConversation(conversationId) {
                try {
                    const response = await fetch(`${this.apiBaseUrl}/conversations/${conversationId}`, {
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    });

                    if (response.ok) {
                        const payload = await response.json();
                        const data = payload && payload.data ? payload.data : payload;
                        this.currentConversationId = conversationId;
                        this.chatTitle.textContent = (data.conversation && data.conversation.title) ? data.conversation.title : 'Conversation';
                        this.renderMessages(data.messages || []);
                        this.renderConversations();
                        this.hideEmptyState();

                        // Close sidebar on mobile
                        if (window.innerWidth <= 768) {
                            toggleSidebar();
                        }
                    }
                } catch (error) {
                    console.error('Failed to load conversation:', error);
                }
            }

            renderMessages(messages) {
                this.messagesContainer.innerHTML = '';

                messages.forEach(message => {
                    this.addMessageToUI(message.content, message.type, message.timestamp);
                });

                this.scrollToBottom();
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || this.isProcessing) return;

                this.isProcessing = true;
                this.sendButton.disabled = true;
                this.messageInput.value = '';

                // Add user message to UI
                this.addMessageToUI(message, 'user');
                this.showTypingIndicator();
                this.hideEmptyState();

                try {
                    const response = await fetch(`${this.apiBaseUrl}/chat/message`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            message: message,
                            conversation_id: this.currentConversationId,
                            language: detectLanguage(message)
                        })
                    });

                    const data = await response.json();

                    if (response.ok) {
                        // Add AI response to UI
                        this.addMessageToUI(data.response, 'ai');

                        // Update conversation ID if new conversation
                        if (data.conversation_id && !this.currentConversationId) {
                            this.currentConversationId = data.conversation_id;
                            this.loadConversations();
                        }

                        // Update chat title if provided
                        if (data.conversation_title) {
                            this.chatTitle.textContent = data.conversation_title;
                        }
                    } else {
                        this.addErrorMessage(data.message || 'An error occurred');
                    }
                } catch (error) {
                    this.addErrorMessage('Network error. Please try again.');
                } finally {
                    this.hideTypingIndicator();
                    this.isProcessing = false;
                    this.sendButton.disabled = false;
                    this.messageInput.focus();
                }
            }

            addMessageToUI(content, type, timestamp = null) {
                const messageCard = document.createElement('div');
                messageCard.className = `message-card ${type}`;

                const time = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();

                messageCard.innerHTML = `
                    <div class="message-body">
                        ${this.escapeHtml(content)}
                    </div>
                    <div class="message-time">${time}</div>
                `;

                this.messagesContainer.appendChild(messageCard);
                this.scrollToBottom();
            }

            addErrorMessage(message) {
                const alert = document.createElement('div');
                alert.className = 'error-alert';
                alert.textContent = message;

                this.messagesContainer.appendChild(alert);
                this.scrollToBottom();
            }

            showTypingIndicator() {
                this.typingIndicator.classList.add('show');
                this.scrollToBottom();
            }

            hideTypingIndicator() {
                this.typingIndicator.classList.remove('show');
            }

            hideEmptyState() {
                this.emptyState.style.display = 'none';
            }

            scrollToBottom() {
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }


            formatTime(timestamp) {
                const date = new Date(timestamp);
                const now = new Date();
                const diffTime = Math.abs(now - date);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                if (diffDays === 1) {
                    return 'Today';
                } else if (diffDays === 2) {
                    return 'Yesterday';
                } else if (diffDays <= 7) {
                    return `${diffDays} days ago`;
                } else {
                    return date.toLocaleDateString();
                }
            }
        }

        // Global functions
        function startNewConversation() {
            window.chatApp.currentConversationId = null;
            window.chatApp.chatTitle.textContent = 'New Conversation';
            window.chatApp.messagesContainer.innerHTML = '';
            window.chatApp.emptyState.style.display = 'flex';
            window.chatApp.renderConversations();
            window.chatApp.messageInput.focus();

            // Close sidebar on mobile
            if (window.innerWidth <= 768) {
                toggleSidebar();
            }
        }

        function sendMessage() {
            window.chatApp.sendMessage();
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        function detectLanguage(text) {
            const hasArabic = /[\u0600-\u06FF]/.test(text);
            return hasArabic ? 'ar' : 'en';
        }

        // Dark mode toggle functionality
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');
            const currentTheme = body.getAttribute('data-theme');

            if (currentTheme === 'dark') {
                body.removeAttribute('data-theme');
                themeIcon.className = 'bi bi-moon-fill';
                localStorage.setItem('theme', 'light');
            } else {
                body.setAttribute('data-theme', 'dark');
                themeIcon.className = 'bi bi-sun-fill';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Initialize theme from localStorage
        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme');
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');

            if (savedTheme === 'dark') {
                body.setAttribute('data-theme', 'dark');
                themeIcon.className = 'bi bi-sun-fill';
            } else {
                body.removeAttribute('data-theme');
                themeIcon.className = 'bi bi-moon-fill';
            }
        }

        // Initialize app when page loads
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize theme first
            initializeTheme();

            // Initialize chat app
            window.chatApp = new WiddxChatApp();

            // Focus on input
            document.getElementById('messageInput').focus();
        });
    </script>
</body>
</html>
