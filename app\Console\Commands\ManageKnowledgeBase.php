<?php

namespace App\Console\Commands;

use App\Services\LearningService;
use App\Services\SimilaritySearchService;
use App\Models\KnowledgeBase;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ManageKnowledgeBase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'knowledge-base:manage 
                            {action : Action to perform (stats|prune|rebuild|search|export)}
                            {--language= : Filter by language}
                            {--min-quality=0.3 : Minimum quality score for pruning}
                            {--max-age=90 : Maximum age in days for pruning}
                            {--min-usage=1 : Minimum usage count for pruning}
                            {--query= : Search query for search action}
                            {--limit=10 : Limit results for search action}
                            {--force : Force action without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage the WIDDX AI knowledge base - view stats, prune entries, rebuild embeddings, search, and export data';

    private LearningService $learningService;
    private SimilaritySearchService $similaritySearchService;

    public function __construct(
        LearningService $learningService,
        SimilaritySearchService $similaritySearchService
    ) {
        parent::__construct();
        $this->learningService = $learningService;
        $this->similaritySearchService = $similaritySearchService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'stats':
                return $this->showStats();
            case 'prune':
                return $this->pruneEntries();
            case 'rebuild':
                return $this->rebuildEmbeddings();
            case 'search':
                return $this->searchEntries();
            case 'export':
                return $this->exportData();
            default:
                $this->error("Unknown action: {$action}");
                $this->info('Available actions: stats, prune, rebuild, search, export');
                return 1;
        }
    }

    /**
     * Show knowledge base statistics
     */
    private function showStats(): int
    {
        $this->info('📊 Knowledge Base Statistics');
        $this->newLine();

        try {
            $stats = $this->learningService->getLearningStats();
            
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Entries', number_format($stats['total_entries'] ?? 0)],
                    ['API Calls Saved', number_format($stats['api_calls_saved'] ?? 0)],
                    ['Average Quality', round($stats['average_quality'] ?? 0, 3)],
                    ['Total Usage Count', number_format($stats['total_usage'] ?? 0)],
                    ['Entries by Language', ''],
                ]
            );

            if (isset($stats['by_language'])) {
                $this->newLine();
                $this->info('📈 Entries by Language:');
                foreach ($stats['by_language'] as $lang => $count) {
                    $this->line("  {$lang}: " . number_format($count));
                }
            }

            if (isset($stats['quality_distribution'])) {
                $this->newLine();
                $this->info('📊 Quality Distribution:');
                foreach ($stats['quality_distribution'] as $range => $count) {
                    $this->line("  {$range}: " . number_format($count));
                }
            }

            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to retrieve statistics: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Prune low-quality entries
     */
    private function pruneEntries(): int
    {
        $minQuality = (float) $this->option('min-quality');
        $maxAge = (int) $this->option('max-age');
        $minUsage = (int) $this->option('min-usage');
        $language = $this->option('language');
        $force = $this->option('force');

        $this->info('🧹 Pruning Knowledge Base Entries');
        $this->newLine();

        // Show what will be pruned
        $query = KnowledgeBase::query()
            ->where('quality_rating', '<', $minQuality)
            ->orWhere('usage_count', '<', $minUsage)
            ->orWhere('created_at', '<', now()->subDays($maxAge));

        if ($language) {
            $query->where('language', $language);
        }

        $toPrune = $query->count();

        if ($toPrune === 0) {
            $this->info('✅ No entries found matching pruning criteria.');
            return 0;
        }

        $this->warn("Found {$toPrune} entries matching pruning criteria:");
        $this->line("  - Quality below: {$minQuality}");
        $this->line("  - Usage below: {$minUsage}");
        $this->line("  - Older than: {$maxAge} days");
        if ($language) {
            $this->line("  - Language: {$language}");
        }

        if (!$force && !$this->confirm('Do you want to proceed with pruning?')) {
            $this->info('Pruning cancelled.');
            return 0;
        }

        try {
            $options = [
                'min_quality' => $minQuality,
                'max_age_days' => $maxAge,
                'min_usage_count' => $minUsage
            ];

            if ($language) {
                $options['language'] = $language;
            }

            $result = $this->learningService->pruneKnowledgeBase($options);
            
            $this->info("✅ Successfully pruned {$result['pruned']} entries.");
            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to prune entries: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Rebuild missing embeddings
     */
    private function rebuildEmbeddings(): int
    {
        $this->info('🔄 Rebuilding Missing Embeddings');
        $this->newLine();

        try {
            $result = $this->similaritySearchService->rebuildMissingEmbeddings();
            
            $this->info("✅ Successfully rebuilt {$result['rebuilt']} embeddings.");
            if ($result['failed'] > 0) {
                $this->warn("⚠️  Failed to rebuild {$result['failed']} embeddings.");
            }
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Failed to rebuild embeddings: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Search knowledge base entries
     */
    private function searchEntries(): int
    {
        $query = $this->option('query');
        $language = $this->option('language') ?? 'en';
        $limit = (int) $this->option('limit');

        if (!$query) {
            $query = $this->ask('Enter search query:');
        }

        if (!$query) {
            $this->error('Search query is required.');
            return 1;
        }

        $this->info("🔍 Searching for: '{$query}' (Language: {$language})");
        $this->newLine();

        try {
            $results = $this->similaritySearchService->searchSimilarQuestions(
                $query,
                $language,
                $limit
            );

            if (empty($results)) {
                $this->info('No matching entries found.');
                return 0;
            }

            $tableData = [];
            foreach ($results as $result) {
                $tableData[] = [
                    substr($result['question'], 0, 50) . '...',
                    substr($result['answer'], 0, 50) . '...',
                    round($result['similarity'], 3),
                    $result['usage_count'],
                    round($result['quality_rating'], 2),
                    $result['created_at']->format('Y-m-d')
                ];
            }

            $this->table(
                ['Question', 'Answer', 'Similarity', 'Usage', 'Quality', 'Created'],
                $tableData
            );

            return 0;
        } catch (\Exception $e) {
            $this->error('Search failed: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Export knowledge base data
     */
    private function exportData(): int
    {
        $language = $this->option('language');
        $filename = 'knowledge_base_export_' . date('Y-m-d_H-i-s') . '.json';
        $filepath = storage_path('app/' . $filename);

        $this->info('📤 Exporting Knowledge Base Data');
        $this->newLine();

        try {
            $query = KnowledgeBase::query();
            
            if ($language) {
                $query->where('language', $language);
                $this->info("Filtering by language: {$language}");
            }

            $entries = $query->orderBy('created_at', 'desc')->get();
            
            $exportData = [
                'exported_at' => now()->toISOString(),
                'total_entries' => $entries->count(),
                'language_filter' => $language,
                'entries' => $entries->map(function ($entry) {
                    return [
                        'id' => $entry->id,
                        'question' => $entry->question,
                        'answer' => $entry->answer,
                        'language' => $entry->language,
                        'confidence_score' => $entry->confidence_score,
                        'usage_count' => $entry->usage_count,
                        'quality_rating' => $entry->quality_rating,
                        'source_model' => $entry->source_model,
                        'created_at' => $entry->created_at->toISOString(),
                        'last_used_at' => $entry->last_used_at?->toISOString()
                    ];
                })
            ];

            file_put_contents($filepath, json_encode($exportData, JSON_PRETTY_PRINT));
            
            $this->info("✅ Exported {$entries->count()} entries to: {$filename}");
            $this->line("Full path: {$filepath}");
            
            return 0;
        } catch (\Exception $e) {
            $this->error('Export failed: ' . $e->getMessage());
            return 1;
        }
    }
}
