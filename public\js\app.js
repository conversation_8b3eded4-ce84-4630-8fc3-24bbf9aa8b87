/**
 * WIDDX AI - Elite Application Bootstrap
 * Main Application Initialization and Coordination
 */

class WiddxApp {
    constructor() {
        this.version = '2.0.0';
        this.modules = {};
        this.isInitialized = false;
        
        this.initializeApp();
    }

    async initializeApp() {
        try {
            console.log(`🚀 WIDDX AI v${this.version} - Initializing Elite Chat Interface`);
            
            // Initialize core modules
            await this.initializeModules();
            
            // Setup global event listeners
            this.setupGlobalEvents();
            
            // Initialize performance monitoring
            this.initializePerformanceMonitoring();
            
            // Mark as initialized
            this.isInitialized = true;
            
            console.log('✅ WIDDX AI - Initialization Complete');
            
            // Dispatch ready event
            this.dispatchReadyEvent();
            
        } catch (error) {
            console.error('❌ WIDDX AI - Initialization Failed:', error);
            this.handleInitializationError(error);
        }
    }

    async initializeModules() {
        // Initialize Theme Manager first (affects visual appearance)
        if (window.WiddxThemeManager) {
            this.modules.themeManager = new WiddxThemeManager();
            window.widdxThemeManager = this.modules.themeManager;
            console.log('✅ Theme Manager initialized');
        }

        // Initialize UI Manager
        if (window.WiddxUIManager) {
            this.modules.uiManager = new WiddxUIManager();
            window.widdxUIManager = this.modules.uiManager;
            console.log('✅ UI Manager initialized');
        }

        // Initialize Chat Core (main functionality)
        if (window.WiddxChatCore) {
            this.modules.chatCore = new WiddxChatCore();
            window.chatApp = this.modules.chatCore; // Maintain backward compatibility
            console.log('✅ Chat Core initialized');
        }

        // Wait for DOM to be fully ready
        if (document.readyState !== 'complete') {
            await new Promise(resolve => {
                window.addEventListener('load', resolve);
            });
        }

        // Focus on message input
        this.focusMessageInput();
    }

    setupGlobalEvents() {
        // Theme change events
        window.addEventListener('themeChange', (e) => {
            console.log(`🎨 Theme changed to: ${e.detail.theme}`);
            this.handleThemeChange(e.detail.theme);
        });

        // UI events
        window.addEventListener('uisidebarShow', () => {
            console.log('📱 Sidebar shown');
        });

        window.addEventListener('uisidebarHide', () => {
            console.log('📱 Sidebar hidden');
        });

        // Error handling
        window.addEventListener('error', (e) => {
            this.handleGlobalError(e);
        });

        window.addEventListener('unhandledrejection', (e) => {
            this.handleUnhandledRejection(e);
        });

        // Visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // Online/offline status
        window.addEventListener('online', () => {
            this.handleOnlineStatus(true);
        });

        window.addEventListener('offline', () => {
            this.handleOnlineStatus(false);
        });
    }

    initializePerformanceMonitoring() {
        // Performance observer for monitoring
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'navigation') {
                        console.log(`📊 Page load time: ${entry.loadEventEnd - entry.loadEventStart}ms`);
                    }
                }
            });
            
            observer.observe({ entryTypes: ['navigation'] });
        }

        // Memory usage monitoring (if available)
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                    console.warn('⚠️ High memory usage detected');
                }
            }, 30000); // Check every 30 seconds
        }
    }

    handleThemeChange(theme) {
        // Update meta theme-color for mobile browsers
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
            const color = theme === 'dark' ? '#0f172a' : '#ffffff';
            metaThemeColor.setAttribute('content', color);
        }

        // Update favicon if needed
        this.updateFavicon(theme);
    }

    updateFavicon(theme) {
        // This could be extended to use different favicons for different themes
        const favicon = document.querySelector('link[rel="icon"]');
        if (favicon && theme === 'dark') {
            // Could switch to a dark-themed favicon
        }
    }

    handleGlobalError(event) {
        console.error('🚨 Global Error:', event.error);
        
        // Show user-friendly error message
        this.showErrorNotification('An unexpected error occurred. Please refresh the page.');
        
        // Report to error tracking service (if configured)
        this.reportError(event.error);
    }

    handleUnhandledRejection(event) {
        console.error('🚨 Unhandled Promise Rejection:', event.reason);
        
        // Prevent the default browser behavior
        event.preventDefault();
        
        // Show user-friendly error message
        this.showErrorNotification('A network error occurred. Please check your connection.');
        
        // Report to error tracking service (if configured)
        this.reportError(event.reason);
    }

    handleVisibilityChange() {
        if (document.hidden) {
            console.log('👁️ Page hidden');
            // Pause any unnecessary operations
        } else {
            console.log('👁️ Page visible');
            // Resume operations, check for updates
            this.checkForUpdates();
        }
    }

    handleOnlineStatus(isOnline) {
        if (isOnline) {
            console.log('🌐 Back online');
            this.showSuccessNotification('Connection restored');
            // Retry failed requests
            this.retryFailedRequests();
        } else {
            console.log('📴 Gone offline');
            this.showWarningNotification('Connection lost. Some features may not work.');
        }
    }

    handleInitializationError(error) {
        // Show fallback UI
        document.body.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;">
                <div style="text-align: center; padding: 2rem;">
                    <h1 style="color: #dc2626; margin-bottom: 1rem;">WIDDX AI - Initialization Error</h1>
                    <p style="color: #6b7280; margin-bottom: 2rem;">Failed to load the chat interface. Please refresh the page.</p>
                    <button onclick="window.location.reload()" style="background: #3b82f6; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.5rem; cursor: pointer;">
                        Refresh Page
                    </button>
                </div>
            </div>
        `;
    }

    focusMessageInput() {
        setTimeout(() => {
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.focus();
            }
        }, 100);
    }

    checkForUpdates() {
        // This could check for application updates
        // For now, just a placeholder
    }

    retryFailedRequests() {
        // This could retry any failed API requests
        // For now, just a placeholder
    }

    showErrorNotification(message) {
        this.showNotification(message, 'error');
    }

    showSuccessNotification(message) {
        this.showNotification(message, 'success');
    }

    showWarningNotification(message) {
        this.showNotification(message, 'warning');
    }

    showNotification(message, type = 'info') {
        // Simple notification system
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            color: white;
            font-family: var(--font-family);
            font-size: 14px;
            z-index: 10000;
            animation: slideIn 0.3s ease;
            background: ${type === 'error' ? '#dc2626' : type === 'success' ? '#059669' : type === 'warning' ? '#d97706' : '#3b82f6'};
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    reportError(error) {
        // This could send errors to a logging service
        // For now, just log to console
        console.error('Error reported:', error);
    }

    dispatchReadyEvent() {
        const event = new CustomEvent('widdxReady', {
            detail: {
                version: this.version,
                modules: Object.keys(this.modules),
                timestamp: Date.now()
            }
        });
        window.dispatchEvent(event);
    }

    // Public API
    getVersion() {
        return this.version;
    }

    getModules() {
        return this.modules;
    }

    isReady() {
        return this.isInitialized;
    }

    restart() {
        window.location.reload();
    }
}

// Initialize the application when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.widdxApp = new WiddxApp();
    });
} else {
    window.widdxApp = new WiddxApp();
}

// Add CSS for notifications
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(notificationStyles);

// Export for debugging
window.WiddxApp = WiddxApp;
