<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Exception;

class WiddxAiService
{
    private DeepSeekService $deepSeekService;
    private GeminiService $geminiService;
    private LearningService $learningService;
    private BrandingService $brandingService;
    private bool $knowledgeBaseEnabled;
    private bool $selfLearningEnabled;

    public function __construct(
        DeepSeekService $deepSeekService,
        GeminiService $geminiService,
        LearningService $learningService,
        BrandingService $brandingService
    ) {
        $this->deepSeekService = $deepSeekService;
        $this->geminiService = $geminiService;
        $this->learningService = $learningService;
        $this->brandingService = $brandingService;
        $this->knowledgeBaseEnabled = config('widdx.knowledge_base.enabled', false);
        $this->selfLearningEnabled = config('widdx.features.self_learning', true);
    }

    /**
     * Process user query through the complete WIDDX AI pipeline
     *
     * @param string $userQuery
     * @param string $language
     * @param string|null $sessionId
     * @return array
     */
    public function processQuery(string $userQuery, string $language = 'en', ?string $sessionId = null): array
    {
        $startTime = microtime(true);

        try {
            Log::info('WIDDX AI: Processing query', [
                'query' => $userQuery,
                'language' => $language,
                'session_id' => $sessionId,
                'knowledge_base_enabled' => $this->knowledgeBaseEnabled
            ]);

            // Step 1: Check knowledge base first (if enabled)
            if ($this->knowledgeBaseEnabled) {
                $knowledgeBaseResult = $this->learningService->queryKnowledgeBase($userQuery, $language);

                if (!empty($knowledgeBaseResult)) {
                    Log::info('WIDDX AI: Answer found in knowledge base', [
                        'session_id' => $sessionId,
                        'confidence' => $knowledgeBaseResult['confidence'] ?? null,
                        'source' => 'knowledge_base'
                    ]);

                    return $this->compileKnowledgeBaseResponse(
                        $userQuery,
                        $knowledgeBaseResult,
                        $language,
                        $sessionId,
                        $startTime
                    );
                }
            }

            // Step 2: Search for information using DeepSeek (fallback)
            $deepSeekData = $this->deepSeekService->search($userQuery, $language);

            // Step 3: Generate response using Gemini with enriched data
            $geminiResponse = $this->geminiService->generateResponse(
                $userQuery,
                $deepSeekData,
                $language
            );

            // If Gemini failed or returned empty, fall back to DeepSeek chat completion content (if available)
            if ((isset($geminiResponse['fallback']) || empty($geminiResponse['response']))
                && !empty($deepSeekData['results'][0]['content'] ?? null)) {
                $geminiResponse['response'] = $deepSeekData['results'][0]['content'];
                // Mark model to reflect WIDDX AI (anonymize source)
                $geminiResponse['model'] = 'WIDDX AI';
                $geminiResponse['fallback'] = false; // Mark as successful fallback
            }

            // Apply branding filter to ensure WIDDX AI identity
            if (!empty($geminiResponse['response'])) {
                $geminiResponse['response'] = $this->brandingService->filterResponse(
                    $geminiResponse['response'],
                    $language
                );
            }

            // Step 4: Learn from this interaction (if enabled)
            if ($this->selfLearningEnabled && !empty($geminiResponse['response'])) {
                // Parameter order: question, answer, language, sourceModel, confidenceScore, metadata
                $this->learningService->learnFromInteraction(
                    $userQuery,
                    $geminiResponse['response'],
                    $language,
                    'external_api',
                    $this->calculateQualityScore($deepSeekData, $geminiResponse),
                    [
                        'deepseek_sources' => count($deepSeekData['results'] ?? []),
                        'session_id' => $sessionId
                    ]
                );
            }

            // Step 5: Compile final response
            $response = $this->compileResponse(
                $userQuery,
                $deepSeekData,
                $geminiResponse,
                $language,
                $sessionId,
                $startTime
            );

            Log::info('WIDDX AI: Query processed successfully', [
                'session_id' => $sessionId,
                'processing_time' => $response['processing_time'],
                'sources_used' => $response['sources_used']
            ]);

            return $response;

        } catch (Exception $e) {
            Log::error('WIDDX AI: Query processing failed', [
                'query' => $userQuery,
                'language' => $language,
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return $this->getErrorResponse($userQuery, $language, $sessionId, $startTime, $e);
        }
    }

    /**
     * Compile response from knowledge base
     *
     * @param string $userQuery
     * @param array $knowledgeBaseResult
     * @param string $language
     * @param string|null $sessionId
     * @param float $startTime
     * @return array
     */
    private function compileKnowledgeBaseResponse(
        string $userQuery,
        array $knowledgeBaseResult,
        string $language,
        ?string $sessionId,
        float $startTime
    ): array {
        $processingTime = round((microtime(true) - $startTime) * 1000, 2);

        return [
            'success' => true,
            'response' => $knowledgeBaseResult['answer'],
            'user_query' => $userQuery,
            'language' => $language,
            'session_id' => $sessionId,
            'sources_used' => 0, // Knowledge base doesn't use external sources
            'processing_time' => $processingTime,
            'timestamp' => now()->toISOString(),
            'model_info' => [
                'search_engine' => 'Knowledge Base',
                'ai_model' => $knowledgeBaseResult['source_model'] ?? 'Unknown',
                'version' => '1.0'
            ],
            'performance' => [
                'knowledge_base_time' => $processingTime,
                'total_processing_time' => $processingTime,
                'cached_response' => true
            ],
            'metadata' => [
                'sources' => [],
                'quality_score' => $knowledgeBaseResult['confidence'],
                'fallback_used' => false,
                'knowledge_base_used' => true,
                'usage_count' => $knowledgeBaseResult['usage_count'] ?? 1,
                'last_used' => $knowledgeBaseResult['last_used'] ?? now()->toISOString()
            ]
        ];
    }

    /**
     * Compile the final response with all metadata
     *
     * @param string $userQuery
     * @param array $deepSeekData
     * @param array $geminiResponse
     * @param string $language
     * @param string|null $sessionId
     * @param float $startTime
     * @return array
     */
    private function compileResponse(
        string $userQuery,
        array $deepSeekData,
        array $geminiResponse,
        string $language,
        ?string $sessionId,
        float $startTime
    ): array {
        $processingTime = round((microtime(true) - $startTime) * 1000, 2); // in milliseconds

        // Check if we have a valid response (not fallback error message)
        $hasValidResponse = !empty($geminiResponse['response']) &&
                           $geminiResponse['response'] !== 'Sorry, the AI service is temporarily unavailable. Please try again later.';

        // Get final response with branding filter
        $finalResponse = $hasValidResponse ? $geminiResponse['response'] :
                        (!empty($deepSeekData['results'][0]['content'] ?? null) ?
                         $this->brandingService->filterResponse($deepSeekData['results'][0]['content'], $language) :
                         $this->brandingService->getBrandedErrorMessage($language));

        // Prepare metadata with branding filter
        $metadata = [
            'sources' => $this->extractSourceMetadata($deepSeekData),
            'quality_score' => $this->calculateQualityScore($deepSeekData, $geminiResponse),
            'fallback_used' => !$hasValidResponse && !empty($deepSeekData['results'][0]['content'] ?? null)
        ];
        $filteredMetadata = $this->brandingService->filterMetadata($metadata);

        return [
            'success' => true,
            'response' => $finalResponse,
            'user_query' => $userQuery,
            'language' => $language,
            'session_id' => $sessionId,
            'sources_used' => count($deepSeekData['results'] ?? []),
            'processing_time' => $processingTime,
            'timestamp' => now()->toISOString(),
            'model_info' => [
                'search_engine' => 'WIDDX AI Research',
                'ai_model' => 'WIDDX AI',
                'version' => '1.0'
            ],
            'performance' => [
                'search_time' => $deepSeekData['search_time'] ?? 0,
                'total_processing_time' => $processingTime,
                'cached_response' => isset($geminiResponse['fallback']) ? false : null
            ],
            'metadata' => $filteredMetadata
        ];
    }

    /**
     * Extract source metadata from DeepSeek data
     *
     * @param array $deepSeekData
     * @return array
     */
    private function extractSourceMetadata(array $deepSeekData): array
    {
        $sources = [];

        if (isset($deepSeekData['results']) && is_array($deepSeekData['results'])) {
            foreach ($deepSeekData['results'] as $result) {
                $sources[] = [
                    'title' => $result['title'] ?? 'Unknown',
                    'source' => $result['source'] ?? 'Unknown',
                    'url' => $result['url'] ?? null,
                    'relevance_score' => $result['relevance_score'] ?? 0
                ];
            }
        }

        return $sources;
    }

    /**
     * Calculate quality score based on available data
     *
     * @param array $deepSeekData
     * @param array $geminiResponse
     * @return float
     */
    private function calculateQualityScore(array $deepSeekData, array $geminiResponse): float
    {
        $score = 0.5; // Base score

        // Add points for having search results
        if (!empty($deepSeekData['results'])) {
            $score += 0.3;

            // Add points based on number of sources
            $sourceCount = count($deepSeekData['results']);
            $score += min(0.2, $sourceCount * 0.04);
        }

        // Add points for successful AI response
        if (!empty($geminiResponse['response']) && !isset($geminiResponse['fallback'])) {
            $score += 0.2;
        }

        // Deduct points for fallback responses
        if (isset($geminiResponse['fallback']) || isset($deepSeekData['fallback'])) {
            $score -= 0.3;
        }

        return round(max(0, min(1, $score)), 2);
    }

    /**
     * Get error response when processing fails
     *
     * @param string $userQuery
     * @param string $language
     * @param string|null $sessionId
     * @param float $startTime
     * @param Exception $exception
     * @return array
     */
    private function getErrorResponse(
        string $userQuery,
        string $language,
        ?string $sessionId,
        float $startTime,
        Exception $exception
    ): array {
        $processingTime = round((microtime(true) - $startTime) * 1000, 2);

        $errorMessage = $language === 'ar' ?
            'عذراً، حدث خطأ أثناء معالجة استفسارك. يرجى المحاولة مرة أخرى.' :
            'Sorry, an error occurred while processing your query. Please try again.';

        return [
            'success' => false,
            'response' => $errorMessage,
            'user_query' => $userQuery,
            'language' => $language,
            'session_id' => $sessionId,
            'sources_used' => 0,
            'processing_time' => $processingTime,
            'timestamp' => now()->toISOString(),
            'error' => [
                'message' => $exception->getMessage(),
                'type' => get_class($exception)
            ],
            'model_info' => [
                'search_engine' => 'DeepSeek',
                'ai_model' => 'Gemini Pro',
                'version' => '1.0'
            ],
            'performance' => [
                'deepseek_search_time' => 0,
                'total_processing_time' => $processingTime,
                'cached_response' => false
            ],
            'metadata' => [
                'sources' => [],
                'quality_score' => 0,
                'fallback_used' => true
            ]
        ];
    }

    /**
     * Update quality rating for a knowledge base entry
     *
     * @param string $userQuery
     * @param string $language
     * @param float $rating
     * @return bool
     */
    public function updateQualityRating(string $userQuery, string $language, float $rating): bool
    {
        if (!$this->knowledgeBaseEnabled) {
            return false;
        }

        // Find knowledge base entry by question hash and language
        $hash = \App\Models\KnowledgeBase::generateQuestionHash($userQuery, $language);
        $entry = \App\Models\KnowledgeBase::byQuestionHash($hash)->byLanguage($language)->first();
        if (!$entry) {
            return false;
        }

        return $this->learningService->updateQualityRating($entry->id, $rating);
    }

    /**
     * Get learning statistics
     *
     * @return array
     */
    public function getLearningStats(): array
    {
        if (!$this->knowledgeBaseEnabled) {
            return [
                'knowledge_base_enabled' => false,
                'total_entries' => 0,
                'api_calls_saved' => 0
            ];
        }

        return $this->learningService->getLearningStats();
    }

    /**
     * Prune low-quality knowledge base entries
     *
     * @param array $options
     * @return array
     */
    public function pruneKnowledgeBase(array $options = []): array
    {
        if (!$this->knowledgeBaseEnabled) {
            return ['pruned' => 0, 'message' => 'Knowledge base disabled'];
        }

        return $this->learningService->pruneKnowledgeBase($options);
    }

    /**
     * Get system health status
     *
     * @return array
     */
    public function getHealthStatus(): array
    {
        $healthStatus = [
            'deepseek' => [
                'available' => $this->deepSeekService->isAvailable(),
                'service' => 'DeepSeek Search API'
            ],
            'gemini' => [
                'available' => $this->geminiService->isAvailable(),
                'service' => 'Google Gemini API'
            ],
            'overall_status' => $this->deepSeekService->isAvailable() && $this->geminiService->isAvailable() ? 'healthy' : 'degraded',
            'timestamp' => now()->toISOString()
        ];

        // Add knowledge base status if enabled
        if ($this->knowledgeBaseEnabled) {
            $learningStats = $this->getLearningStats();
            $healthStatus['knowledge_base'] = [
                'enabled' => true,
                'total_entries' => $learningStats['total_entries'] ?? 0,
                'service' => 'Self-Learning Knowledge Base'
            ];
        }

        return $healthStatus;
    }
}
