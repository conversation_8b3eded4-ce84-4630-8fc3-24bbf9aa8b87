# WIDDX AI Platform

🚀 **Intelligent Chat System powered by DEEPSEEK and GEMINI**

WIDDX AI is a sophisticated chat platform that combines the search capabilities of DEEPSEEK with the conversational intelligence of GEMINI to deliver accurate, contextual, and intelligent responses.

## 🌟 Features

### Core Capabilities
- **Dual AI Integration**: Combines DEEPSEEK for search and GEMINI for conversational AI
- **Multi-language Support**: English and Arabic language support
- **Real-time Chat**: Sub-2-second response times
- **Quality Monitoring**: 90%+ information accuracy with quality scoring
- **Conversation Management**: Persistent chat history and session management

### Security & Performance
- **Rate Limiting**: Configurable request limits per minute/hour
- **Input Validation**: Comprehensive sanitization and malicious content detection
- **Caching**: Redis-based caching for optimal performance
- **Error Handling**: Robust fallback mechanisms
- **Health Monitoring**: Real-time system health checks

### Technical Features
- **Laravel 10 Backend**: Modern PHP framework with clean architecture
- **RESTful API**: Well-documented API endpoints
- **Middleware Security**: Custom validation and rate limiting
- **Database Management**: MySQL with optimized schema
- **Modern Frontend**: Responsive web interface

## 🛠️ Installation

### Prerequisites
- PHP 8.1 or higher
- Composer
- MySQL 5.7+ or 8.0+
- Redis (optional, for caching)
- Node.js & NPM (for frontend assets)

### Quick Setup

1. **Clone and Install Dependencies**
   ```bash
   git clone <repository-url> widdx-ai
   cd widdx-ai
   composer install
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

3. **Configure API Keys**
   Edit `.env` file and add your API keys:
   ```env
   DEEPSEEK_API_KEY=your_deepseek_api_key_here
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

4. **Database Setup**
   ```bash
   # Configure database in .env
   DB_DATABASE=widdx_ai
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   
   # Run migrations
   php artisan migrate
   ```

5. **Start the Application**
   ```bash
   php artisan serve
   ```

   Visit `http://localhost:8000` to access the chat interface.

## 🔧 Configuration

### API Keys Setup

#### DEEPSEEK API
1. Visit [DEEPSEEK API Portal](https://api.deepseek.com)
2. Create an account and generate API key
3. Add to `.env`: `DEEPSEEK_API_KEY=your_key_here`

#### GEMINI API
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create API key for Gemini
3. Add to `.env`: `GEMINI_API_KEY=your_key_here`

### Performance Tuning

```env
# Response time targets (milliseconds)
WIDDX_TARGET_RESPONSE_TIME=2000
WIDDX_MAX_PROCESSING_TIME=30000

# Caching
WIDDX_ENABLE_CACHING=true
WIDDX_CACHE_DRIVER=redis

# Rate limiting
WIDDX_MAX_REQUESTS_PER_MINUTE=60
WIDDX_MAX_REQUESTS_PER_HOUR=1000
```

## 📚 API Documentation

### Base URL
```
http://localhost:8000/api
```

### Endpoints

#### Send Message
```http
POST /api/chat/message
Content-Type: application/json

{
  "message": "Your question here",
  "session_id": "optional_session_id",
  "language": "en"
}
```

**Response:**
```json
{
  "success": true,
  "response": "AI generated response",
  "session_id": "session_12345",
  "processing_time": 1250,
  "quality_score": 0.95,
  "sources_count": 3,
  "language": "en"
}
```

#### Get Conversation
```http
GET /api/chat/conversation/{sessionId}
```

#### Health Check
```http
GET /api/health
```

**Response:**
```json
{
  "status": "healthy",
  "services": {
    "deepseek": "online",
    "gemini": "online",
    "database": "connected",
    "cache": "connected"
  },
  "performance": {
    "avg_response_time": 1200,
    "success_rate": 98.5
  }
}
```

## 🏗️ Architecture

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Laravel API   │    │   AI Services   │
│   (Blade/JS)    │◄──►│   (Backend)     │◄──►│   (DEEPSEEK +   │
│                 │    │                 │    │    GEMINI)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   MySQL DB      │    │   Redis Cache   │
│                 │    │   (Conversations│    │   (Performance) │
│                 │    │    & Messages)  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Service Layer

- **WiddxAiService**: Main orchestrator
- **DeepSeekService**: Search functionality
- **GeminiService**: Conversational AI
- **Middleware**: Security and validation
- **Models**: Data persistence

## 🔒 Security Features

### Input Validation
- Maximum message length: 4,000 characters
- Malicious content detection
- SQL injection prevention
- XSS protection
- Input sanitization

### Rate Limiting
- Per-IP request limiting
- Configurable thresholds
- Graceful degradation
- Rate limit headers

### API Security
- CSRF protection
- Content-type validation
- Request size limits
- Error message sanitization

## 📊 Monitoring

### Health Checks
- Service availability monitoring
- Database connectivity
- Cache system status
- API response times

### Performance Metrics
- Average response time
- Success/failure rates
- Quality scores
- Cache hit rates

### Logging
- Request/response logging
- Error tracking
- Performance monitoring
- Security event logging

## 🌍 Multi-language Support

### Supported Languages
- **English (en)**: Default language
- **Arabic (ar)**: Full RTL support

### Language Detection
- Automatic language detection
- Manual language selection
- Context-aware responses

## 🚀 Deployment

### Production Setup

1. **Environment Configuration**
   ```env
   APP_ENV=production
   APP_DEBUG=false
   WIDDX_ENABLE_CACHING=true
   WIDDX_CACHE_DRIVER=redis
   ```

2. **Optimization**
   ```bash
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   composer install --optimize-autoloader --no-dev
   ```

3. **Web Server Configuration**
   - Configure Nginx/Apache
   - Set up SSL certificates
   - Configure reverse proxy
   - Set up monitoring

### Docker Deployment

```dockerfile
# Dockerfile example
FROM php:8.1-fpm

# Install dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy application
COPY . .

# Install dependencies
RUN composer install --optimize-autoloader --no-dev

# Set permissions
RUN chown -R www-data:www-data /var/www

EXPOSE 9000
CMD ["php-fpm"]
```

## 🧪 Testing

### Running Tests
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run with coverage
php artisan test --coverage
```

### API Testing
```bash
# Test health endpoint
curl -X GET http://localhost:8000/api/health

# Test chat message
curl -X POST http://localhost:8000/api/chat/message \
  -H "Content-Type: application/json" \
  -d '{"message":"Hello, how are you?","language":"en"}'
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Standards
- Follow PSR-12 coding standards
- Write comprehensive tests
- Document new features
- Use meaningful commit messages

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Troubleshooting

**Common Issues:**

1. **API Keys Not Working**
   - Verify API keys are correct
   - Check API quotas and limits
   - Ensure network connectivity

2. **Database Connection Issues**
   - Verify database credentials
   - Check database server status
   - Run `php artisan migrate:status`

3. **Performance Issues**
   - Enable Redis caching
   - Check API response times
   - Monitor system resources

### Getting Help

- Check the [Issues](../../issues) page
- Review the [Wiki](../../wiki)
- Contact support: <EMAIL>

## 🔮 Roadmap

### Upcoming Features
- [ ] Voice input/output support
- [ ] File upload and analysis
- [ ] Advanced analytics dashboard
- [ ] Multi-tenant support
- [ ] Plugin system
- [ ] Mobile applications
- [ ] Advanced AI model integration

### Version History

- **v1.0.0** - Initial release with core functionality
- **v1.1.0** - Enhanced security and performance
- **v1.2.0** - Multi-language support

---

**Built with ❤️ by the WIDDX Team**

For more information, visit [widdx.ai](https://widdx.ai)
