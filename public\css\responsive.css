/* WIDDX AI - Elite Responsive Design */
/* Advanced Mobile-First Responsive System */

/* Elite Mobile Responsive Design */
@media (max-width: 768px) {
    :root {
        --sidebar-width: 300px;
        --max-width: 100%;
        --content-padding: 16px;
        --header-height: 56px;
    }

    .sidebar {
        position: fixed;
        left: -100%;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: left var(--transition-slow);
        box-shadow: var(--shadow-2xl);
    }

    .sidebar.show {
        left: 0;
    }

    .main-chat {
        width: 100%;
    }

    .message-body {
        max-width: 92%;
        padding: 16px 20px;
    }

    .mobile-toggle {
        display: block !important;
    }

    .messages-container {
        padding: var(--content-padding);
    }

    .chat-input-container {
        padding: var(--content-padding);
    }

    .input-wrapper {
        gap: 12px;
    }

    .message-input {
        min-height: 48px;
        padding: 14px 18px;
        font-size: var(--font-size-base);
    }

    .send-btn {
        width: 48px;
        height: 48px;
        font-size: var(--font-size-base);
    }

    .empty-state {
        padding: 60px var(--content-padding);
    }

    .empty-state-icon {
        width: 64px;
        height: 64px;
        margin-bottom: 24px;
    }

    .empty-state-icon i {
        font-size: 28px;
    }

    .empty-state h4 {
        font-size: var(--font-size-2xl);
    }

    .empty-state p {
        font-size: var(--font-size-base);
    }

    .chat-title {
        font-size: var(--font-size-base);
    }

    .new-chat-btn {
        margin: 16px;
        padding: 14px 18px;
    }

    .conversation-item {
        padding: 14px 16px;
    }

    .sidebar-overlay.show {
        display: block;
    }
}

/* Tablet Responsive Design */
@media (min-width: 769px) and (max-width: 1024px) {
    :root {
        --sidebar-width: 260px;
        --max-width: 700px;
        --content-padding: 20px;
    }

    .message-body {
        max-width: 90%;
    }

    .empty-state {
        padding: 70px var(--content-padding);
    }

    .empty-state h4 {
        font-size: var(--font-size-2xl);
    }
}

/* Large Desktop */
@media (min-width: 1440px) {
    :root {
        --sidebar-width: 320px;
        --max-width: 900px;
        --content-padding: 32px;
    }

    .empty-state {
        padding: 100px var(--content-padding);
    }

    .empty-state-icon {
        width: 96px;
        height: 96px;
    }

    .empty-state-icon i {
        font-size: 42px;
    }

    .empty-state h4 {
        font-size: var(--font-size-4xl);
    }

    .empty-state p {
        font-size: var(--font-size-xl);
    }
}

/* Ultra-wide Displays */
@media (min-width: 1920px) {
    :root {
        --sidebar-width: 360px;
        --max-width: 1000px;
        --content-padding: 40px;
    }
}

/* Portrait Orientation */
@media (orientation: portrait) and (max-width: 768px) {
    .message-body {
        max-width: 95%;
    }

    .input-wrapper {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .send-btn {
        width: 100%;
        height: 48px;
        border-radius: var(--border-radius-lg);
    }
}

/* Landscape Orientation on Mobile */
@media (orientation: landscape) and (max-height: 500px) {
    .empty-state {
        padding: 40px var(--content-padding);
    }

    .empty-state-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 16px;
    }

    .empty-state-icon i {
        font-size: 20px;
    }

    .empty-state h4 {
        font-size: var(--font-size-lg);
        margin-bottom: 8px;
    }

    .empty-state p {
        font-size: var(--font-size-sm);
    }

    .chat-header {
        height: 48px;
    }

    .chat-input-container {
        padding: 12px var(--content-padding);
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .sidebar-header i,
    .empty-state-icon i,
    .theme-toggle i {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .conversation-item,
    .new-chat-btn,
    .theme-toggle,
    .send-btn {
        min-height: 44px;
    }

    .message-input {
        min-height: 44px;
    }

    /* Remove hover effects on touch devices */
    .conversation-item:hover,
    .new-chat-btn:hover,
    .theme-toggle:hover,
    .send-btn:hover,
    .message-body:hover {
        transform: none;
        box-shadow: var(--shadow-sm);
    }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
    .typing-dot,
    .empty-state-icon,
    .sidebar-header i {
        animation: none;
    }

    .message-card {
        animation: none;
    }

    .conversation-item:hover,
    .new-chat-btn:hover,
    .theme-toggle:hover,
    .send-btn:hover {
        transform: none;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .chat-input-container,
    .typing-indicator,
    .theme-toggle,
    .mobile-toggle,
    .new-chat-btn {
        display: none !important;
    }
    
    .main-chat {
        width: 100% !important;
    }
    
    .message-body {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
        break-inside: avoid;
    }

    .message-card {
        margin-bottom: 16px;
        page-break-inside: avoid;
    }

    .chat-messages {
        overflow: visible !important;
        height: auto !important;
    }

    .messages-container {
        max-width: none !important;
        padding: 0 !important;
    }
}
