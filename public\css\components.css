/* WIDDX AI - Elite UI Components */
/* Premium Component Library */

/* Elite New Chat Button */
.new-chat-btn {
    margin: 20px;
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    color: var(--text-primary);
    padding: 16px 20px;
    border-radius: var(--border-radius-xl);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.new-chat-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--brand-gradient-subtle);
    transition: left var(--transition-normal);
    z-index: -1;
}

.new-chat-btn:hover {
    background: var(--hover-bg);
    border-color: var(--brand-primary);
    color: var(--brand-primary);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.new-chat-btn:hover::before {
    left: 0;
}

.new-chat-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.new-chat-btn i {
    transition: transform var(--transition-normal);
}

.new-chat-btn:hover i {
    transform: rotate(90deg);
}

/* Elite Conversation Items */
.conversation-item {
    padding: 16px 20px;
    margin-bottom: 4px;
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.conversation-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--brand-gradient-subtle);
    transition: left var(--transition-normal);
    z-index: -1;
    opacity: 0.1;
}

.conversation-item:hover {
    background: var(--hover-bg);
    border-color: var(--bg-tertiary);
    box-shadow: var(--shadow-sm);
    transform: translateX(4px);
}

.conversation-item:hover::before {
    left: 0;
}

.conversation-item.active {
    background: var(--brand-light);
    border-color: var(--brand-primary);
    color: var(--brand-secondary);
    box-shadow: var(--shadow-md);
    transform: translateX(8px);
}

.conversation-item.active::before {
    left: 0;
    opacity: 0.2;
}

.conversation-title {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    margin-bottom: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: var(--line-height-tight);
}

.conversation-preview {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: var(--line-height-tight);
}

.conversation-time {
    font-size: var(--font-size-xs);
    color: var(--text-quaternary);
    margin-top: 6px;
    font-weight: var(--font-weight-normal);
}

/* Mobile Toggle Button */
.mobile-toggle {
    display: none;
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    cursor: pointer;
    padding: 10px;
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-normal);
}

.mobile-toggle:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
    transform: scale(1.1);
}

/* Chat Title */
.chat-title {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
    font-size: var(--font-size-lg);
    letter-spacing: -0.02em;
    background: var(--brand-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Elite Theme Toggle */
.theme-toggle {
    background: var(--bg-primary);
    border: 2px solid var(--bg-tertiary);
    border-radius: var(--border-radius-lg);
    padding: 12px;
    cursor: pointer;
    transition: all var(--transition-normal);
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.theme-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--brand-gradient-subtle);
    transition: left var(--transition-normal);
    z-index: -1;
}

.theme-toggle:hover {
    background: var(--hover-bg);
    color: var(--brand-primary);
    box-shadow: var(--shadow-md);
    transform: scale(1.1);
    border-color: var(--brand-primary);
}

.theme-toggle:hover::before {
    left: 0;
}

.theme-toggle i {
    transition: transform var(--transition-normal);
}

.theme-toggle:hover i {
    transform: rotate(180deg);
}
