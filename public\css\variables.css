/* WIDDX AI - Elite Design System Variables */
/* World-Class CSS Custom Properties */

:root {
    /* Elite Color Palette - Light Theme */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-quaternary: #e2e8f0;
    --bg-chat: #ffffff;
    --bg-input: #ffffff;
    --bg-sidebar: #fafbfc;
    --bg-glass: rgba(255, 255, 255, 0.8);
    --bg-overlay: rgba(0, 0, 0, 0.05);
    
    /* Advanced Text Hierarchy */
    --text-primary: #0f172a;
    --text-secondary: #334155;
    --text-tertiary: #64748b;
    --text-quaternary: #94a3b8;
    --text-muted: #cbd5e1;
    --text-inverse: #ffffff;
    --text-accent: #1e40af;
    
    /* Sophisticated Brand System */
    --brand-primary: #3b82f6;
    --brand-secondary: #1d4ed8;
    --brand-tertiary: #1e40af;
    --brand-light: #dbeafe;
    --brand-lighter: #eff6ff;
    --brand-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
    --brand-gradient-subtle: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #93c5fd 100%);
    --brand-gradient-reverse: linear-gradient(315deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
    
    /* Elite Message System */
    --user-bg: var(--brand-gradient);
    --user-bg-hover: linear-gradient(135deg, #2563eb 0%, #1d4ed8 50%, #1e40af 100%);
    --user-text: #ffffff;
    --user-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
    --ai-bg: #f8fafc;
    --ai-bg-hover: #f1f5f9;
    --ai-text: var(--text-primary);
    --ai-border: #e2e8f0;
    --ai-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    
    /* Advanced Interactive States */
    --hover-bg: #f8fafc;
    --hover-border: #cbd5e1;
    --active-bg: #f1f5f9;
    --active-border: #94a3b8;
    --focus-ring: 0 0 0 3px rgba(59, 130, 246, 0.12);
    --focus-ring-error: 0 0 0 3px rgba(239, 68, 68, 0.12);
    --focus-ring-success: 0 0 0 3px rgba(34, 197, 94, 0.12);
    
    /* Elite Shadow System */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
    
    /* Premium Layout System */
    --sidebar-width: 280px;
    --header-height: 64px;
    --input-container-height: 88px;
    --border-radius-xs: 4px;
    --border-radius-sm: 6px;
    --border-radius: 8px;
    --border-radius-md: 10px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-2xl: 20px;
    --border-radius-3xl: 24px;
    --border-radius-full: 9999px;
    --max-width: 800px;
    --content-padding: 24px;
    
    /* Advanced Typography Scale */
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui, "Helvetica Neue", "Noto Sans", sans-serif;
    --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, monospace;
    --font-size-xs: 11px;
    --font-size-sm: 12px;
    --font-size-base: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 18px;
    --font-size-2xl: 20px;
    --font-size-3xl: 24px;
    --font-size-4xl: 28px;
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Animation & Transition System */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
