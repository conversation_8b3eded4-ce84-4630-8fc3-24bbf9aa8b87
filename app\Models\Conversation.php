<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Conversation extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'session_id',
        'title',
        'language',
        'user_ip',
        'user_agent',
        'total_messages',
        'last_activity_at',
        'metadata'
    ];

    protected $casts = [
        'last_activity_at' => 'datetime',
        'metadata' => 'array'
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($conversation) {
            if (empty($conversation->session_id)) {
                $conversation->session_id = Str::uuid()->toString();
            }
        });
    }

    /**
     * Get all messages for this conversation
     */
    public function messages(): HasMany
    {
        return $this->hasMany(Message::class)->orderBy('created_at');
    }

    /**
     * Get the latest message in this conversation
     */
    public function latestMessage()
    {
        return $this->hasOne(Message::class)->latest();
    }

    /**
     * Update the conversation's last activity and message count
     */
    public function updateActivity(): void
    {
        $this->update([
            'last_activity_at' => now(),
            'total_messages' => $this->messages()->count()
        ]);
    }

    /**
     * Generate a title for the conversation based on the first message
     */
    public function generateTitle(): void
    {
        if (empty($this->title)) {
            $firstMessage = $this->messages()->where('type', 'user')->first();
            if ($firstMessage) {
                $title = Str::limit($firstMessage->content, 50, '...');
                $this->update(['title' => $title]);
            }
        }
    }

    /**
     * Scope to get conversations by language
     */
    public function scopeByLanguage($query, string $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Scope to get recent conversations
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('last_activity_at', '>=', now()->subDays($days));
    }

    /**
     * Get conversation statistics
     */
    public function getStatistics(): array
    {
        $messages = $this->messages;
        
        return [
            'total_messages' => $messages->count(),
            'user_messages' => $messages->where('type', 'user')->count(),
            'ai_messages' => $messages->where('type', 'ai')->count(),
            'average_response_time' => $messages->where('type', 'ai')->avg('processing_time'),
            'total_sources_used' => $messages->where('type', 'ai')->sum('sources_used'),
            'average_quality_score' => $messages->where('type', 'ai')->avg('quality_score'),
            'duration' => $this->created_at->diffInMinutes($this->last_activity_at ?? now())
        ];
    }
}