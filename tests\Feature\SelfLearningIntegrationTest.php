<?php

namespace Tests\Feature;

use App\Models\KnowledgeBase;
use App\Models\User;
use App\Models\Conversation;
use App\Models\Message;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SelfLearningIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        // Enable self-learning features for tests
        config([
            'widdx.features.self_learning' => true,
            'widdx.features.similarity_search' => true,
            'widdx.features.knowledge_base' => true,
            'widdx.learning.auto_learn' => true,
            'widdx.similarity.threshold' => 0.8,
            'widdx.embedding.provider' => 'mock', // Use mock provider for tests
        ]);
    }

    /** @test */
    public function it_can_store_knowledge_base_entry_manually()
    {
        $entry = KnowledgeBase::create([
            'question_hash' => hash('sha256', 'What is <PERSON><PERSON>?'),
            'question' => 'What is <PERSON><PERSON>?',
            'answer' => 'Laravel is a PHP web application framework.',
            'language' => 'en',
            'confidence_score' => 0.9,
            'quality_rating' => 0.8,
            'source_model' => 'gemini-pro',
            'usage_count' => 0,
        ]);

        $this->assertDatabaseHas('knowledge_bases', [
            'question' => 'What is Laravel?',
            'answer' => 'Laravel is a PHP web application framework.',
            'language' => 'en',
        ]);

        $this->assertEquals('What is Laravel?', $entry->question);
        $this->assertEquals('Laravel is a PHP web application framework.', $entry->answer);
        $this->assertEquals(0.9, $entry->confidence_score);
    }

    /** @test */
    public function it_can_update_usage_count_when_entry_is_accessed()
    {
        $entry = KnowledgeBase::create([
            'question_hash' => hash('sha256', 'Test question'),
            'question' => 'Test question',
            'answer' => 'Test answer',
            'language' => 'en',
            'confidence_score' => 0.8,
            'quality_rating' => 0.7,
            'source_model' => 'gemini-pro',
            'usage_count' => 0,
        ]);

        $this->assertEquals(0, $entry->usage_count);

        // Simulate accessing the entry
        $entry->increment('usage_count');
        $entry->update(['last_used_at' => now()]);

        $entry->refresh();
        $this->assertEquals(1, $entry->usage_count);
        $this->assertNotNull($entry->last_used_at);
    }

    /** @test */
    public function it_can_update_quality_rating_based_on_feedback()
    {
        $entry = KnowledgeBase::create([
            'question_hash' => hash('sha256', 'Quality test question'),
            'question' => 'Quality test question',
            'answer' => 'Quality test answer',
            'language' => 'en',
            'confidence_score' => 0.8,
            'quality_rating' => 0.5,
            'source_model' => 'gemini-pro',
            'usage_count' => 1,
        ]);

        $originalQuality = $entry->quality_rating;

        // Simulate positive feedback
        $newRating = 0.9;
        $entry->update(['quality_rating' => $newRating]);

        $entry->refresh();
        $this->assertEquals($newRating, $entry->quality_rating);
        $this->assertGreaterThan($originalQuality, $entry->quality_rating);
    }

    /** @test */
    public function it_can_filter_entries_by_language()
    {
        // Create entries in different languages
        KnowledgeBase::create([
            'question_hash' => hash('sha256', 'English question'),
            'question' => 'English question',
            'answer' => 'English answer',
            'language' => 'en',
            'confidence_score' => 0.9,
            'quality_rating' => 0.8,
            'source_model' => 'gemini-pro',
            'usage_count' => 1,
        ]);

        KnowledgeBase::create([
            'question_hash' => hash('sha256', 'Pregunta en español'),
            'question' => 'Pregunta en español',
            'answer' => 'Respuesta en español',
            'language' => 'es',
            'confidence_score' => 0.9,
            'quality_rating' => 0.8,
            'source_model' => 'gemini-pro',
            'usage_count' => 1,
        ]);

        $englishEntries = KnowledgeBase::where('language', 'en')->get();
        $spanishEntries = KnowledgeBase::where('language', 'es')->get();

        $this->assertCount(1, $englishEntries);
        $this->assertCount(1, $spanishEntries);
        $this->assertEquals('English question', $englishEntries->first()->question);
        $this->assertEquals('Pregunta en español', $spanishEntries->first()->question);
    }

    /** @test */
    public function it_can_find_entries_by_exact_question_hash()
    {
        $question = 'Exact match test question';
        $questionHash = hash('sha256', $question);

        KnowledgeBase::create([
            'question_hash' => $questionHash,
            'question' => $question,
            'answer' => 'Exact match answer',
            'language' => 'en',
            'confidence_score' => 0.9,
            'quality_rating' => 0.8,
            'source_model' => 'gemini-pro',
            'usage_count' => 0,
        ]);

        $foundEntry = KnowledgeBase::where('question_hash', $questionHash)->first();

        $this->assertNotNull($foundEntry);
        $this->assertEquals($question, $foundEntry->question);
        $this->assertEquals('Exact match answer', $foundEntry->answer);
    }

    /** @test */
    public function it_can_prune_low_quality_entries()
    {
        // Create a low-quality entry
        $lowQualityEntry = KnowledgeBase::create([
            'question_hash' => hash('sha256', 'Low quality question'),
            'question' => 'Low quality question',
            'answer' => 'Poor answer',
            'language' => 'en',
            'confidence_score' => 0.3,
            'quality_rating' => 0.2,
            'source_model' => 'gemini-pro',
            'usage_count' => 0,
            'created_at' => now()->subDays(100),
        ]);

        // Create a high-quality entry
        $highQualityEntry = KnowledgeBase::create([
            'question_hash' => hash('sha256', 'High quality question'),
            'question' => 'High quality question',
            'answer' => 'Excellent answer',
            'language' => 'en',
            'confidence_score' => 0.9,
            'quality_rating' => 0.9,
            'source_model' => 'gemini-pro',
            'usage_count' => 10,
        ]);

        // Prune entries with quality below 0.3
        $prunedCount = KnowledgeBase::where('quality_rating', '<', 0.3)->delete();

        $this->assertEquals(1, $prunedCount);
        $this->assertDatabaseMissing('knowledge_bases', [
            'id' => $lowQualityEntry->id,
        ]);
        $this->assertDatabaseHas('knowledge_bases', [
            'id' => $highQualityEntry->id,
        ]);
    }

    /** @test */
    public function it_can_get_knowledge_base_statistics()
    {
        // Create test entries
        KnowledgeBase::create([
            'question_hash' => hash('sha256', 'Question 1'),
            'question' => 'Question 1',
            'answer' => 'Answer 1',
            'language' => 'en',
            'confidence_score' => 0.8,
            'quality_rating' => 0.7,
            'source_model' => 'gemini-pro',
            'usage_count' => 5,
        ]);

        KnowledgeBase::create([
            'question_hash' => hash('sha256', 'Question 2'),
            'question' => 'Question 2',
            'answer' => 'Answer 2',
            'language' => 'es',
            'confidence_score' => 0.9,
            'quality_rating' => 0.8,
            'source_model' => 'gemini-pro',
            'usage_count' => 3,
        ]);

        $totalEntries = KnowledgeBase::count();
        $totalUsage = KnowledgeBase::sum('usage_count');
        $averageQuality = KnowledgeBase::avg('quality_rating');
        $entriesByLanguage = KnowledgeBase::groupBy('language')
            ->selectRaw('language, count(*) as count')
            ->pluck('count', 'language')
            ->toArray();

        $this->assertEquals(2, $totalEntries);
        $this->assertEquals(8, $totalUsage);
        $this->assertEquals(0.75, $averageQuality);
        $this->assertArrayHasKey('en', $entriesByLanguage);
        $this->assertArrayHasKey('es', $entriesByLanguage);
        $this->assertEquals(1, $entriesByLanguage['en']);
        $this->assertEquals(1, $entriesByLanguage['es']);
    }

    /** @test */
    public function it_can_handle_chat_api_endpoints_structure()
    {
        // Test the API endpoint structure without actually calling external APIs
        $this->actingAs($this->user);

        // Test learning stats endpoint
        $response = $this->getJson('/api/chat/learning-stats');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_entries',
            'api_calls_saved',
            'average_quality',
            'by_language',
        ]);

        // Create a conversation and message for rating test
        $conversation = Conversation::create([
            'user_id' => $this->user->id,
            'title' => 'Test Conversation',
        ]);

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'content' => 'Test message',
            'is_user' => false,
            'model_used' => 'gemini-pro',
            'processing_time' => 1.5,
            'quality_score' => 0.8,
        ]);

        // Test rate response endpoint
        $response = $this->postJson('/api/chat/rate-response', [
            'message_id' => $message->id,
            'rating' => 0.9,
            'feedback' => 'Great response!',
        ]);
        $response->assertStatus(200);

        // Test prune knowledge base endpoint
        $response = $this->postJson('/api/chat/prune-knowledge-base', [
            'min_quality' => 0.3,
            'max_age_days' => 90,
            'min_usage_count' => 1,
        ]);
        $response->assertStatus(200);
    }
}