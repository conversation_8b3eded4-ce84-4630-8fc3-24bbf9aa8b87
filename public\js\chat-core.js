/**
 * WIDDX AI - Elite Chat Core Module
 * Advanced Chat Functionality and Message Handling
 */

class WiddxChatCore {
    constructor() {
        this.currentConversationId = null;
        this.conversations = [];
        this.isProcessing = false;
        this.apiBaseUrl = '/api';

        this.initializeElements();
        this.bindEvents();
        this.loadConversations();
        this.initializeUI();
    }

    initializeElements() {
        this.chatMessages = document.getElementById('chatMessages');
        this.messagesContainer = this.chatMessages.querySelector('.messages-container');
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.typingIndicator = document.getElementById('typingIndicator');
        this.conversationsList = document.getElementById('conversationsList');
        this.chatTitle = document.getElementById('chatTitle');
        this.emptyState = document.getElementById('emptyState');
        this.sidebar = document.getElementById('sidebar');
        this.sidebarOverlay = document.getElementById('sidebarOverlay');
    }

    bindEvents() {
        // Message input events
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        this.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
        });

        // Send button event
        this.sendButton.addEventListener('click', () => {
            this.sendMessage();
        });

        // Sidebar overlay event
        this.sidebarOverlay.addEventListener('click', () => {
            this.hideSidebar();
        });

        // Auto-resize textarea on page load
        this.autoResizeTextarea();
    }

    initializeUI() {
        // Ensure typing indicator is hidden on startup
        this.hideTypingIndicator();

        // Ensure empty state is visible if no conversations
        if (this.conversations.length === 0) {
            this.emptyState.style.display = 'flex';
        }
    }

    async loadConversations() {
        try {
            // For now, initialize with empty conversations array
            // This will be connected to actual API later
            this.conversations = [];
            this.renderConversations();

            if (this.conversations.length === 0) {
                this.emptyState.style.display = 'flex';
            } else {
                this.emptyState.style.display = 'none';
            }
        } catch (error) {
            console.error('Error loading conversations:', error);
            this.conversations = [];
        }
    }

    renderConversations() {
        if (!this.conversationsList) return;

        this.conversationsList.innerHTML = '';

        this.conversations.forEach(conversation => {
            const conversationElement = this.createConversationElement(conversation);
            this.conversationsList.appendChild(conversationElement);
        });
    }

    createConversationElement(conversation) {
        const element = document.createElement('div');
        element.className = `conversation-item ${conversation.id === this.currentConversationId ? 'active' : ''}`;
        element.onclick = () => this.selectConversation(conversation.id);

        const title = conversation.title || 'New Conversation';
        const preview = conversation.last_message ?
            conversation.last_message.substring(0, 50) + '...' :
            'No messages yet';
        const time = conversation.updated_at ?
            new Date(conversation.updated_at).toLocaleDateString() :
            'Today';

        element.innerHTML = `
            <div class="conversation-title">${this.escapeHtml(title)}</div>
            <div class="conversation-preview">${this.escapeHtml(preview)}</div>
            <div class="conversation-time">${time}</div>
        `;

        return element;
    }

    async selectConversation(conversationId) {
        this.currentConversationId = conversationId;
        this.emptyState.style.display = 'none';

        const conversation = this.conversations.find(c => c.id === conversationId);
        if (conversation) {
            this.chatTitle.textContent = conversation.title || 'Conversation';
            await this.loadMessages(conversationId);
        }

        this.renderConversations();
        this.messageInput.focus();

        // Close sidebar on mobile
        if (window.innerWidth <= 768) {
            this.hideSidebar();
        }
    }

    async loadMessages(conversationId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/conversations/${conversationId}/messages`);
            if (response.ok) {
                const messages = await response.json();
                this.renderMessages(messages);
            }
        } catch (error) {
            console.error('Error loading messages:', error);
            this.addErrorMessage('Failed to load conversation messages.');
        }
    }

    renderMessages(messages) {
        this.messagesContainer.innerHTML = '';

        messages.forEach(message => {
            this.addMessageToUI(message.content, message.type, message.timestamp);
        });

        this.scrollToBottom();
    }

    async sendMessage() {
        const content = this.messageInput.value.trim();
        if (!content || this.isProcessing) return;

        this.isProcessing = true;
        this.messageInput.value = '';
        this.autoResizeTextarea();
        this.sendButton.disabled = true;

        // Add user message to UI immediately
        this.addMessageToUI(content, 'user');
        this.emptyState.style.display = 'none';

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Simulate API call for demo purposes
            // This will be connected to actual API later
            await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

            // Simulate AI response
            const responses = [
                "Thank you for testing the new modular architecture! I'm impressed by the clean separation of concerns and maintainable code structure.",
                "The modular design makes the codebase much more organized and easier to maintain. Great work on the refactoring!",
                "I can see the benefits of the new architecture - separate CSS files, modular JavaScript, and reusable Blade components. This is excellent!",
                "The elite design system with proper CSS custom properties and component-based architecture is truly impressive!",
                "This modular approach will make future development much more efficient and the code much more maintainable."
            ];

            const randomResponse = responses[Math.floor(Math.random() * responses.length)];

            // Update conversation info
            if (!this.currentConversationId) {
                this.currentConversationId = 'demo-conversation-' + Date.now();
                this.chatTitle.textContent = 'Demo Conversation';
            }

            // Add AI response to UI
            this.addMessageToUI(randomResponse, 'ai');

            // Update conversations list
            await this.loadConversations();

        } catch (error) {
            console.error('Error sending message:', error);
            this.addErrorMessage('Failed to send message. Please try again.');
        } finally {
            this.hideTypingIndicator();
            this.isProcessing = false;
            this.sendButton.disabled = false;
            this.messageInput.focus();
        }
    }

    addMessageToUI(content, type, timestamp = null) {
        const messageCard = document.createElement('div');
        messageCard.className = `message-card ${type}`;

        const time = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();

        messageCard.innerHTML = `
            <div class="message-body">
                ${this.escapeHtml(content)}
            </div>
            <div class="message-time">${time}</div>
        `;

        this.messagesContainer.appendChild(messageCard);
        this.scrollToBottom();
    }

    addErrorMessage(message) {
        const alert = document.createElement('div');
        alert.className = 'error-alert';
        alert.textContent = message;

        this.messagesContainer.appendChild(alert);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        this.typingIndicator.classList.add('show');
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        this.typingIndicator.classList.remove('show');
    }

    autoResizeTextarea() {
        const textarea = this.messageInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 140) + 'px';
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }

    showSidebar() {
        this.sidebar.classList.add('show');
        this.sidebarOverlay.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    hideSidebar() {
        this.sidebar.classList.remove('show');
        this.sidebarOverlay.classList.remove('show');
        document.body.style.overflow = '';
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    detectLanguage(text) {
        const hasArabic = /[\u0600-\u06FF]/.test(text);
        return hasArabic ? 'ar' : 'en';
    }
}

// Export for use in other modules
window.WiddxChatCore = WiddxChatCore;
