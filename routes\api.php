<?php

use App\Http\Controllers\Api\ChatController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| WIDDX AI Chat API Routes
|--------------------------------------------------------------------------
*/

// Chat endpoints with WIDDX middleware
Route::prefix('chat')->middleware('widdx')->group(function () {
    // Send message and get AI response
    Route::post('/message', [ChatController::class, 'sendMessage']);
    
    // Get conversation history
    Route::get('/conversation/{sessionId}', [ChatController::class, 'getConversation']);
    
    // Get recent conversations
    Route::get('/conversations', [ChatController::class, 'getRecentConversations']);
    
    // Rate response quality for learning
    Route::post('/rate', [ChatController::class, 'rateResponse']);
    
    // Get learning statistics
    Route::get('/learning/stats', [ChatController::class, 'getLearningStats']);
    
    // Prune knowledge base
    Route::post('/learning/prune', [ChatController::class, 'pruneKnowledgeBase']);
    
    // System health check
    Route::get('/health', [ChatController::class, 'getHealthStatus']);
});

// Additional API endpoints with WIDDX middleware
Route::middleware('widdx')->group(function () {
    // Get conversation history (alternative endpoint)
    Route::get('/conversations/{conversationId}', [ChatController::class, 'getConversation']);
    
    // Get recent conversations (alternative endpoint)
    Route::get('/conversations', [ChatController::class, 'getRecentConversations']);
});

// Public health endpoint (no authentication required)
Route::get('/health', [ChatController::class, 'getHealthStatus']);
